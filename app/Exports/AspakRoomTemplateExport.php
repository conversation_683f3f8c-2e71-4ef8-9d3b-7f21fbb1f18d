<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class AspakRoomTemplateExport implements FromArray, WithHeadings, WithStyles, WithColumnWidths, WithTitle
{
    public function array(): array
    {
        return [
            [
                '01',
                'Pelayanan Medik dan <PERSON>',
                '',
                'BRANCH'
            ],
            [
                '0101',
                'Pelayanan Rawat Jalan',
                '01',
                'BRANCH'
            ],
            [
                '010101',
                '<PERSON><PERSON><PERSON>k Sp. Penyakit Dalam',
                '0101',
                'LEAF'
            ],
            [
                '010102',
                '<PERSON>uangan Klinik Sp. Kesehatan <PERSON>',
                '0101',
                'LEAF'
            ],
            [
                '0102',
                'Pelayanan Gawat Darurat',
                '01',
                'BRANCH'
            ],
            [
                '010201',
                'Umum',
                '0102',
                'LEAF'
            ],
            [
                '010202',
                'Ruangan Triase',
                '0102',
                'LEAF'
            ]
        ];
    }

    public function headings(): array
    {
        return [
            'kode' => 'Kode',
            'nama' => 'Nama',
            'parent' => 'Parent',
            'tree' => 'Tree'
        ];
    }

    public function styles(Worksheet $sheet)
    {
        // Set row height for better spacing
        $sheet->getRowDimension('1')->setRowHeight(25);
        for ($i = 2; $i <= 8; $i++) {
            $sheet->getRowDimension($i)->setRowHeight(20);
        }

        return [
            // Professional header styling
            1 => [
                'font' => [
                    'bold' => true,
                    'size' => 12,
                    'color' => ['rgb' => 'FFFFFF'],
                    'name' => 'Calibri',
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '2C3E50'], // Professional dark navy
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_MEDIUM,
                        'color' => ['rgb' => '34495E'],
                    ],
                ],
            ],
            // Data rows styling with alternating colors
            '2:8' => [
                'font' => [
                    'size' => 11,
                    'name' => 'Calibri',
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => 'D5D8DC'],
                    ],
                ],
                'alignment' => [
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
            ],
            // Alternating row colors for better readability
            2 => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'FFFFFF'], // White
                ],
            ],
            3 => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'F8F9FA'], // Light gray
                ],
            ],
            4 => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'FFFFFF'], // White
                ],
            ],
            5 => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'F8F9FA'], // Light gray
                ],
            ],
            6 => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'FFFFFF'], // White
                ],
            ],
            7 => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'F8F9FA'], // Light gray
                ],
            ],
            8 => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'FFFFFF'], // White
                ],
            ],
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 15, // kode - adequate for alphanumeric codes
            'B' => 40, // nama - wider for longer service room names
            'C' => 15, // parent - same as kode for consistency
            'D' => 18, // tree - adequate for BRANCH/LEAF
        ];
    }

    public function title(): string
    {
        return 'Template Import ASPAK Service Room';
    }
}