<?php

namespace App\Http\Controllers\AssetManagement;

use App\Models\Room;
use App\Models\RoomCategory;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use App\Exports\AssetPositionExport;
use App\Http\Controllers\Controller;
use Maatwebsite\Excel\Facades\Excel;
use Yajra\DataTables\Facades\DataTables;


class AssetPositionController extends Controller
{

    function index()
    {
        if (!hasPermissionInGuard('Kartu Inventaris Ruangan - View')) {
            abort(403, "Unauthorized action.");
        }

        $title = "Kartu inventaris ruangan";
        $breadcrumbs = ["Manajemen Aset", "Kartu inventaris ruangan"];
        $roomCategories = RoomCategory::all();

        $userId = Auth::id() ?? null;
        if ($userId) {
            $guard = "user";
        } else {
            $guard = "employee";
        }

        return view('asset-management.asset-position.index', compact('title', "breadcrumbs", "roomCategories", "guard"));
    }

    function list(Request $request)
    {
        if (!hasPermissionInGuard('Kartu Inventaris Ruangan - View')) {
            abort(403, "Unauthorized action.");
        }
        if ($request->ajax()) {
            $data = Room::leftJoin('employees', 'rooms.pic_room', '=', 'employees.id')
                ->leftJoin('assets', 'rooms.id', '=', 'assets.document_room_id')
                ->when($request->category, function ($query) use ($request) {
                    if ($request->category != "all") {
                        $query->where("rooms.room_category", $request->category);
                    }
                })
                ->when($request->guard, function ($query) {
                    if (request('guard') == 'employee') {
                        $query->where('rooms.pic_room', auth()->guard('employee')->user()->id);
                    }
                })
                ->select('rooms.id', "rooms.room_name", "rooms.room_category", "rooms.room_code", "rooms.building_name", 'employees.employee_name AS pic_name', DB::raw("COUNT(assets.id) as total_asset"))
                ->groupBy('rooms.id', 'rooms.room_name', 'rooms.room_code', 'rooms.pic_room', 'rooms.building_name', "rooms.room_category", 'employees.employee_name')
                ->orderBy("room_code", "ASC");

            return DataTables::eloquent($data)
                ->addIndexColumn()
                ->addColumn("action", function ($row) {
                    return '<a href="#modal-dialog" class="btn btn-sm btn-outline-warning btn-show" data-bs-toggle="modal" data-id="' . $row->id . '"><i class="fas fa-search"></i></a>';
                })
                ->rawColumns(["action"])
                ->make("true");
        }
    }

    function show(Room $room)
    {
        $room->load('pic')->loadCount('assets');

        return response()->json([
            "data" => $room,
        ], 200);
    }

    function asset_list(Room $room)
    {
        if (!hasPermissionInGuard('Kartu Inventaris Ruangan - View')) {
            abort(403, "Unauthorized action.");
        }
        if (request()->ajax()) {
            $data = $room->assets()
                ->join("items", "items.id", "=", "assets.item_id")
                ->join("asset_entries", "asset_entries.id", "=", "assets.asset_entry_id")
                ->join("distributors", "distributors.id", "=", "asset_entries.distributor_id")
                ->select("qr_code", "items.item_code", "assets.asset_name", "asset_entries.brand", "distributors.distributor_name", "assets.unit_price", "register_code", "serial_number");

            return DataTables::eloquent($data)
                ->addIndexColumn()
                ->addColumn("action", function ($row) {
                    return '<a href="#modal-dialog" class="btn btn-sm btn-outline-primary btn-show" data-bs-toggle="modal" data-id="' . $row->id . '"><i class="fas fa-search"></i></a>';
                })
                ->rawColumns(["action"])
                ->make("true");
        }
    }

    public function exportExcel(Request $request, Room $room)
    {
        if (!hasPermissionInGuard('Kartu Inventaris Ruangan - View')) {
            abort(403, "Unauthorized action.");
        }

        $roomData = $this->show($room);

        if ($roomData instanceof JsonResponse) {
            $roomData = json_decode($roomData->getContent(), true)['data'];
        } else {
            $roomData = $roomData;
        }

        $assets = $room->assets()
            ->join("items", "items.id", "=", "assets.item_id")
            ->join("asset_entries", "asset_entries.id", "=", "assets.asset_entry_id")
            ->join("distributors", "distributors.id", "=", "asset_entries.distributor_id")
            ->select("assets.qr_code", "items.item_code", "assets.asset_name", "asset_entries.brand", "distributors.distributor_name", "assets.unit_price", "assets.register_code", "assets.serial_number")
            ->get();


        return Excel::download(new AssetPositionExport($roomData, $assets), 'assets_position_' . $room->room_code . '.xlsx');
    }
}
