<?php

namespace App\Http\Controllers\AssetManagement;

use App\Http\Controllers\Controller;
use App\Models\AssetLocationHistory;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class PositionChangeController extends Controller
{

    function index()
    {
        if (!hasPermissionInGuard('Perubahan Posisi - View')) {
            abort(403, "Unauthorized action.");
        }

        $title = "Perubahan Posisi";
        $breadcrumbs = ["Manajemen Aset", "Perubahan Posisi"];

        return view('asset-management.position-change.index', compact('title', 'breadcrumbs'));
    }

    function list(Request $request)
    {
        if (!hasPermissionInGuard('Perubahan Posisi - View')) {
            abort(403, "Unauthorized action.");
        }
        if ($request->ajax()) {
            $data = AssetLocationHistory::join("assets", "assets.id", "asset_location_histories.asset_id")
                ->join("rooms", "rooms.id", "asset_location_histories.room_id")
                ->leftJoin("rooms as prev_room", "prev_room.id", "asset_location_histories.previous_room_id")
                ->leftJoin("rooms as kir_room", "kir_room.id", "assets.document_room_id")
                ->join("items", "items.id", "assets.item_id")
                ->select("asset_location_histories.id", "asset_location_histories.scan_time", "rooms.room_name", "assets.register_code", "assets.qr_code", "assets.serial_number", "items.item_name", "items.item_code", "prev_room.room_name as prev_room_name", "kir_room.room_name as kir_room_name")
                ->filterRuangan()
                ->filterAsset()
                ->filterDateRange()
                ->latest("scan_time");

            return DataTables::eloquent($data)
                ->addIndexColumn()
                ->addColumn("register_code", function ($row) {
                    return $row->item_code . "<br>" . '<span class="text-danger">' .  $row->register_code . '</span>';
                })
                ->editColumn("scan_time", function ($row) {
                    return Carbon::parse($row->scan_time)->format("d/m/Y H:i:s");
                })
                ->addColumn("action", function ($row) {
                    return '<a href="javascript:;" class="btn btn-sm btn-outline-warning btn-view" data-id="' . $row->id . '"><i class="fas fa-search"></i></a>';
                })
                ->rawColumns(['register_code', 'action'])
                ->make(true);
        }
    }
}
