<?php

namespace App\Http\Controllers\MasterData;

use App\Http\Controllers\Controller;
use App\Models\AspakItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Yajra\DataTables\Facades\DataTables;

class AspakItemController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $title = "ASPAK Item";
        $breadcrumbs = ["Master Data", "Aspak", "Item"];

        return view('master-data.aspak.item.index', compact('title', 'breadcrumbs'));
    }

    /**
     * Get data for DataTables.
     */
    public function list(Request $request)
    {
        try {
            $query = AspakItem::with(['parent', 'createdBy', 'updatedBy'])
                ->select('aspak_items.*');

            // Apply search filter
            if ($request->has('search') && !empty($request->search)) {
                $searchTerm = $request->search;
                $query->where(function ($q) use ($searchTerm) {
                    $q->where('item_name', 'like', "%{$searchTerm}%")
                      ->orWhere('item_code', 'like', "%{$searchTerm}%")
                      ->orWhere('item_synonym', 'like', "%{$searchTerm}%");
                });
            }

            // Apply tree filter
            if ($request->has('tree') && !empty($request->tree)) {
                $query->where('tree', $request->tree);
            }

            $items = $query->orderBy('item_name')->get();

            return DataTables::of($items)
                ->addIndexColumn()
                ->addColumn('level', function ($item) {
                    return $this->calculateLevel($item);
                })
                ->addColumn('item_code_display', function ($item) {
                    return $item->item_code ?? '-';
                })
                ->addColumn('item_synonym_display', function ($item) {
                    return $item->item_synonym ?? '-';
                })
                ->addColumn('parent_name', function ($item) {
                    return $item->parent ? $item->parent->item_name : '-';
                })
                ->addColumn('tree_badge', function ($item) {
                    $badgeClass = $item->tree === 'BRANCH' ? 'bg-primary' : 'bg-success';
                    return '<span class="badge ' . $badgeClass . '">' . $item->tree . '</span>';
                })
                ->addColumn('created_info', function ($item) {
                    $createdBy = $item->createdBy ? $item->createdBy->name : 'System';
                    return $createdBy . '<br><small class="text-muted">' . $item->created_at->format('d/m/Y H:i') . '</small>';
                })
                ->addColumn('updated_info', function ($item) {
                    $updatedBy = $item->updatedBy ? $item->updatedBy->name : 'System';
                    return $updatedBy . '<br><small class="text-muted">' . $item->updated_at->format('d/m/Y H:i') . '</small>';
                })
                ->addColumn('action', function ($item) {
                    return view('components.master.aspak-item.action-buttons', compact('item'))->render();
                })
                ->rawColumns(['tree_badge', 'created_info', 'updated_info', 'action'])
                ->make(true);
        } catch (\Exception $e) {
            Log::error('Error in AspakItemController@list: ' . $e->getMessage());
            return response()->json(['error' => 'Terjadi kesalahan saat mengambil data'], 500);
        }
    }

    /**
     * Get parent groups for Select2.
     */
    public function parentGroups(Request $request)
    {
        try {
            $query = AspakItem::where('tree', 'BRANCH')
                ->select('id', 'item_name as text')
                ->orderBy('item_name');

            if ($request->has('q') && !empty($request->q)) {
                $query->where('item_name', 'like', '%' . $request->q . '%');
            }

            // Exclude current item when editing
            if ($request->has('exclude_id') && !empty($request->exclude_id)) {
                $query->where('id', '!=', $request->exclude_id);
            }

            $items = $query->get();

            return response()->json($items);
        } catch (\Exception $e) {
            Log::error('Error in AspakItemController@parentGroups: ' . $e->getMessage());
            return response()->json(['error' => 'Terjadi kesalahan saat mengambil data parent'], 500);
        }
    }

    /**
     * Get specific item for editing.
     */
    public function edit($id)
    {
        try {
            $item = AspakItem::findOrFail($id);
            return response()->json($item);
        } catch (\Exception $e) {
            Log::error('Error in AspakItemController@edit: ' . $e->getMessage());
            return response()->json(['error' => 'Data tidak ditemukan'], 404);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        try {
            DB::beginTransaction();

            $item = AspakItem::findOrFail($id);

            // Check if item has children
            $childrenCount = AspakItem::where('parent_id', $id)->count();
            if ($childrenCount > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tidak dapat menghapus item yang memiliki sub-item. Hapus sub-item terlebih dahulu.'
                ], 400);
            }

            // Set deleted_by before soft delete
            $item->deleted_by = Auth::id();
            $item->save();
            $item->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Data berhasil dihapus'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error in AspakItemController@destroy: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menghapus data'
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            // Validation rules
            $rules = [
                'item_name' => 'required|string|max:255',
                'item_code' => 'nullable|string|max:50|unique:aspak_items,item_code',
                'item_synonym' => 'nullable|string|max:255',
                'tree' => 'required|in:BRANCH,LEAF',
                'parent_id' => 'nullable|exists:aspak_items,id'
            ];

            // Additional validation for LEAF items
            if ($request->tree === 'LEAF') {
                $rules['parent_id'] = 'required|exists:aspak_items,id';
                
                // Validate that parent is a BRANCH
                $validator = Validator::make($request->all(), $rules);
                $validator->after(function ($validator) use ($request) {
                    if ($request->parent_id) {
                        $parent = AspakItem::find($request->parent_id);
                        if ($parent && $parent->tree !== 'BRANCH') {
                            $validator->errors()->add('parent_id', 'Parent harus berupa BRANCH');
                        }
                    }
                });
            } else {
                // BRANCH items cannot have parent
                $rules['parent_id'] = 'nullable';
                $validator = Validator::make($request->all(), $rules);
                $validator->after(function ($validator) use ($request) {
                    if ($request->parent_id && $request->tree === 'BRANCH') {
                        $validator->errors()->add('parent_id', 'BRANCH tidak boleh memiliki parent');
                    }
                });
            }

            if (isset($validator)) {
                if ($validator->fails()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Validasi gagal',
                        'errors' => $validator->errors()
                    ], 422);
                }
            } else {
                $validator = Validator::make($request->all(), $rules);
                if ($validator->fails()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Validasi gagal',
                        'errors' => $validator->errors()
                    ], 422);
                }
            }

            DB::beginTransaction();

            $item = new AspakItem();
            $item->item_name = $request->item_name;
            $item->item_code = $request->item_code;
            $item->item_synonym = $request->item_synonym;
            $item->tree = $request->tree;
            $item->parent_id = $request->tree === 'LEAF' ? $request->parent_id : null;
            $item->created_by = Auth::id();
            $item->updated_by = Auth::id();
            $item->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Data berhasil disimpan',
                'data' => $item
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error in AspakItemController@store: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menyimpan data'
            ], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        try {
            $item = AspakItem::findOrFail($id);

            // Validation rules
            $rules = [
                'item_name' => 'required|string|max:255',
                'item_code' => 'nullable|string|max:50|unique:aspak_items,item_code,' . $id,
                'item_synonym' => 'nullable|string|max:255',
                'tree' => 'required|in:BRANCH,LEAF',
                'parent_id' => 'nullable|exists:aspak_items,id'
            ];

            $validator = Validator::make($request->all(), $rules);

            // Additional validations
            $validator->after(function ($validator) use ($request, $item, $id) {
                // Check if changing from BRANCH to LEAF
                if ($item->tree === 'BRANCH' && $request->tree === 'LEAF') {
                    $childrenCount = AspakItem::where('parent_id', $id)->count();
                    if ($childrenCount > 0) {
                        $validator->errors()->add('tree', 'Tidak dapat mengubah BRANCH menjadi LEAF karena memiliki sub-item');
                    }
                }

                // Validate parent for LEAF items
                if ($request->tree === 'LEAF') {
                    if (!$request->parent_id) {
                        $validator->errors()->add('parent_id', 'LEAF harus memiliki parent');
                    } else {
                        $parent = AspakItem::find($request->parent_id);
                        if ($parent && $parent->tree !== 'BRANCH') {
                            $validator->errors()->add('parent_id', 'Parent harus berupa BRANCH');
                        }
                        
                        // Check for circular reference
                        if ($this->wouldCreateCircularReference($id, $request->parent_id)) {
                            $validator->errors()->add('parent_id', 'Tidak dapat memilih item ini sebagai parent karena akan membuat referensi circular');
                        }
                    }
                } else {
                    // BRANCH items cannot have parent
                    if ($request->parent_id) {
                        $validator->errors()->add('parent_id', 'BRANCH tidak boleh memiliki parent');
                    }
                }
            });

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validasi gagal',
                    'errors' => $validator->errors()
                ], 422);
            }

            DB::beginTransaction();

            $item->item_name = $request->item_name;
            $item->item_code = $request->item_code;
            $item->item_synonym = $request->item_synonym;
            $item->tree = $request->tree;
            $item->parent_id = $request->tree === 'LEAF' ? $request->parent_id : null;
            $item->updated_by = Auth::id();
            $item->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Data berhasil diperbarui',
                'data' => $item
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error in AspakItemController@update: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memperbarui data'
            ], 500);
        }
    }

    /**
     * Import data from Excel file.
     */
    public function import(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'file' => 'required|file|mimes:xlsx,xls|max:10240'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'File tidak valid',
                    'errors' => $validator->errors()
                ], 422);
            }

            $file = $request->file('file');
            $spreadsheet = IOFactory::load($file->getPathname());
            $worksheet = $spreadsheet->getActiveSheet();
            $rows = $worksheet->toArray();

            // Remove header row
            array_shift($rows);

            $successCount = 0;
            $errorCount = 0;
            $errors = [];
            $rowNumber = 2; // Start from row 2 (after header)

            DB::beginTransaction();

            foreach ($rows as $row) {
                try {
                    // Skip empty rows
                    if (empty(array_filter($row))) {
                        $rowNumber++;
                        continue;
                    }

                    $itemName = trim($row[0] ?? '');
                    $itemCode = trim($row[1] ?? '') ?: null;
                    $itemSynonym = trim($row[2] ?? '') ?: null;
                    $tree = strtoupper(trim($row[3] ?? ''));
                    $parentName = trim($row[4] ?? '') ?: null;

                    // Validate required fields
                    if (empty($itemName)) {
                        $errors[] = "Baris {$rowNumber}: Nama item tidak boleh kosong";
                        $errorCount++;
                        $rowNumber++;
                        continue;
                    }

                    if (!in_array($tree, ['BRANCH', 'LEAF'])) {
                        $errors[] = "Baris {$rowNumber}: Tree harus BRANCH atau LEAF";
                        $errorCount++;
                        $rowNumber++;
                        continue;
                    }

                    // Check for duplicate item_code
                    if ($itemCode && AspakItem::where('item_code', $itemCode)->exists()) {
                        $errors[] = "Baris {$rowNumber}: Kode item '{$itemCode}' sudah ada";
                        $errorCount++;
                        $rowNumber++;
                        continue;
                    }

                    $parentId = null;
                    if ($tree === 'LEAF') {
                        if (empty($parentName)) {
                            $errors[] = "Baris {$rowNumber}: LEAF harus memiliki parent";
                            $errorCount++;
                            $rowNumber++;
                            continue;
                        }

                        $parent = AspakItem::where('item_name', $parentName)
                            ->where('tree', 'BRANCH')
                            ->first();

                        if (!$parent) {
                            $errors[] = "Baris {$rowNumber}: Parent '{$parentName}' tidak ditemukan atau bukan BRANCH";
                            $errorCount++;
                            $rowNumber++;
                            continue;
                        }

                        $parentId = $parent->id;
                    }

                    // Create the item
                    AspakItem::create([
                        'item_name' => $itemName,
                        'item_code' => $itemCode,
                        'item_synonym' => $itemSynonym,
                        'tree' => $tree,
                        'parent_id' => $parentId,
                        'created_by' => Auth::id(),
                        'updated_by' => Auth::id()
                    ]);

                    $successCount++;
                } catch (\Exception $e) {
                    $errors[] = "Baris {$rowNumber}: " . $e->getMessage();
                    $errorCount++;
                }

                $rowNumber++;
            }

            DB::commit();

            $message = "Import selesai. Berhasil: {$successCount}, Gagal: {$errorCount}";
            $response = [
                'success' => true,
                'message' => $message,
                'summary' => [
                    'success_count' => $successCount,
                    'error_count' => $errorCount,
                    'errors' => $errors
                ]
            ];

            return response()->json($response);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error in AspakItemController@import: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat import data'
            ], 500);
        }
    }

    /**
     * Download Excel template.
     */
    public function downloadTemplate()
    {
        try {
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();

            // Set headers
            $headers = ['Nama Item', 'Kode Item', 'Sinonim Item', 'Tree (BRANCH/LEAF)', 'Parent (untuk LEAF)'];
            $sheet->fromArray($headers, null, 'A1');

            // Add sample data
            $sampleData = [
                ['Elektronik', 'ELK001', 'Electronics', 'BRANCH', ''],
                ['Laptop', 'LPT001', 'Notebook', 'LEAF', 'Elektronik'],
                ['Smartphone', 'SPH001', 'Handphone', 'LEAF', 'Elektronik']
            ];
            $sheet->fromArray($sampleData, null, 'A2');

            // Style the header
            $headerStyle = [
                'font' => ['bold' => true],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'E2E8F0']
                ]
            ];
            $sheet->getStyle('A1:E1')->applyFromArray($headerStyle);

            // Auto-size columns
            foreach (range('A', 'E') as $column) {
                $sheet->getColumnDimension($column)->setAutoSize(true);
            }

            $writer = new Xlsx($spreadsheet);
            $filename = 'template_aspak_items_' . date('Y-m-d_H-i-s') . '.xlsx';
            $tempPath = storage_path('app/temp/' . $filename);

            // Ensure temp directory exists
            if (!file_exists(dirname($tempPath))) {
                mkdir(dirname($tempPath), 0755, true);
            }

            $writer->save($tempPath);

            return response()->download($tempPath, $filename)->deleteFileAfterSend(true);
        } catch (\Exception $e) {
            Log::error('Error in AspakItemController@downloadTemplate: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat membuat template'
            ], 500);
        }
    }

    /**
     * Calculate item level in hierarchy.
     */
    private function calculateLevel($item, $level = 0)
    {
        if (!$item->parent_id) {
            return $level;
        }

        $parent = AspakItem::find($item->parent_id);
        if (!$parent) {
            return $level;
        }

        return $this->calculateLevel($parent, $level + 1);
    }

    /**
     * Check if setting parent would create circular reference.
     */
    private function wouldCreateCircularReference($itemId, $parentId)
    {
        if ($itemId == $parentId) {
            return true;
        }

        $parent = AspakItem::find($parentId);
        if (!$parent || !$parent->parent_id) {
            return false;
        }

        return $this->wouldCreateCircularReference($itemId, $parent->parent_id);
    }
}