<?php

namespace App\Imports;

use App\Models\AspakServiceRoom;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\WithBatchInserts;

/**
 * AspakRoomImport - Optimized Excel Import for ASPAK Service Room Data
 *
 * This class handles the import of ASPAK Service Room data from Excel files with the following features:
 * - Two-phase import process to handle parent-child relationships
 * - Comprehensive error handling with detailed error collection
 * - Database transaction management with rollback capability
 * - Batch processing for optimal performance
 * - Memory optimization for large import files
 * - Validation with custom error messages
 * - Duplicate detection within the same batch
 *
 * Import Process:
 * 1. First Pass: Insert all records with parent_id set to null
 * 2. Second Pass: Resolve and update parent_id relationships based on parent codes
 *
 * Expected Excel columns: kode, nama, parent, tree
 *
 * @package App\Imports
 * <AUTHOR>
 * @version 2.0 - Optimized Version
 */
class AspakRoomImport implements ToCollection, WithHeadingRow, WithValidation, WithBatchInserts
{
    use Importable;

    /**
     * Success counter for tracking processed records
     */
    public $successCount = 0;

    /**
     * Error collection for comprehensive error reporting
     */
    public $errors = [];

    /**
     * Batch tracking for duplicate detection
     */
    private $batchCodes = [];

    /**
     * Parent mapping from import data
     */
    private $parentMapping = [];

    /**
     * Process the imported collection with optimized two-phase approach
     *
     * This method implements a two-phase import strategy:
     * Phase 1: Insert all records with parent_id as null to avoid foreign key constraints
     * Phase 2: Resolve and update parent_id relationships based on parent codes
     *
     * Features:
     * - Database transaction management with rollback capability
     * - Comprehensive error collection and handling
     * - Batch processing for optimal performance
     * - Memory optimization for large datasets
     * - Detailed logging for debugging purposes
     *
     * @param Collection $rows The imported data collection from Excel
     * @return void
     * @throws \Exception When critical errors occur during import
     */
    public function collection(Collection $rows)
    {
        // Start database transaction for atomicity
        DB::beginTransaction();

        try {
            Log::info('AspakRoomImport: Starting import process', [
                'total_rows' => $rows->count(),
                'user_id' => getAuthUserId()
            ]);

            // Filter out empty rows and prepare data
            $validRows = $this->filterAndPrepareRows($rows);

            if (empty($validRows)) {
                Log::warning('AspakRoomImport: No valid rows found');
                DB::rollBack();
                return;
            }

            // Phase 1: Insert all records with parent_id = null
            $this->insertRecordsFirstPass($validRows);

            // Phase 2: Resolve and update parent_id relationships
            $this->resolveParentRelationships($validRows);

            DB::commit();

            Log::info('AspakRoomImport: Import completed successfully', [
                'success_count' => $this->successCount,
                'error_count' => count($this->errors)
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('AspakRoomImport: Import failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * Filter empty rows and prepare data for processing
     */
    private function filterAndPrepareRows(Collection $rows): array
    {
        $validRows = [];
        $rowIndex = 2; // Start from row 2 (accounting for header)

        foreach ($rows as $row) {
            // Skip completely empty rows
            if (empty(array_filter($row->toArray()))) {
                $rowIndex++;
                continue;
            }

            // Validate required fields
            if (empty($row['kode']) || empty($row['nama']) || empty($row['tree'])) {
                $this->errors[] = [
                    'row' => $rowIndex,
                    'error' => 'Required fields missing (kode, nama, or tree)',
                    'data' => $row->toArray()
                ];
                $rowIndex++;
                continue;
            }

            // Check for duplicate codes within batch
            if (in_array($row['kode'], $this->batchCodes)) {
                $this->errors[] = [
                    'row' => $rowIndex,
                    'error' => 'Duplicate code found in import file: ' . $row['kode'],
                    'data' => $row->toArray()
                ];
                $rowIndex++;
                continue;
            }

            // Check if code already exists in database
            if (AspakServiceRoom::where('room_service_code', $row['kode'])->exists()) {
                $this->errors[] = [
                    'row' => $rowIndex,
                    'error' => 'Code already exists in database: ' . $row['kode'],
                    'data' => $row->toArray()
                ];
                $rowIndex++;
                continue;
            }

            $this->batchCodes[] = $row['kode'];

            // Store parent mapping for phase 2
            if (!empty($row['parent'])) {
                $this->parentMapping[$row['kode']] = $row['parent'];
            }

            $validRows[] = [
                'row_index' => $rowIndex,
                'data' => $row->toArray()
            ];

            $rowIndex++;
        }

        return $validRows;
    }

    /**
     * First Pass: Insert all records with parent_id set to null
     *
     * This method handles the initial insertion of all records without
     * establishing parent-child relationships to avoid foreign key constraint issues.
     *
     * Features:
     * - Batch insertion for performance optimization
     * - Duplicate detection within the same batch
     * - Individual error handling per record
     * - Memory-efficient processing
     *
     * @param array $validRows The validated data rows
     * @return void
     */
    private function insertRecordsFirstPass(array $validRows): void
    {
        $dataToInsert = [];
        $now = now();
        $userId = getAuthUserId();

        foreach ($validRows as $rowData) {
            $row = $rowData['data'];

            $dataToInsert[] = [
                'room_service_code' => $row['kode'],
                'room_service_name' => $row['nama'],
                'parent_id' => null, // Always null in first pass
                'tree' => $row['tree'],
                'created_by' => $userId,
                'created_at' => $now,
                'updated_at' => $now
            ];

            $this->successCount++;
        }

        // Batch insert for performance
        if (!empty($dataToInsert)) {
            AspakServiceRoom::insert($dataToInsert);

            Log::info('AspakRoomImport: Phase 1 completed', [
                'inserted_count' => count($dataToInsert)
            ]);
        }
    }

    /**
     * Second Pass: Resolve and update parent-child relationships
     *
     * This method processes all imported records to establish proper
     * parent-child relationships based on the parent codes provided in the import data.
     *
     * Features:
     * - Handles cases where parent records appear later than children in import file
     * - Supports both existing and newly imported parent records
     * - Efficient bulk updates to minimize database queries
     * - Comprehensive error handling for missing parent references
     *
     * @param array $validRows The validated data rows
     * @return void
     */
    private function resolveParentRelationships(array $validRows): void
    {
        if (empty($this->parentMapping)) {
            Log::info('AspakRoomImport: No parent relationships to resolve');
            return;
        }

        // Get all inserted records for this batch
        $insertedRecords = AspakServiceRoom::whereIn('room_service_code', $this->batchCodes)
            ->get()
            ->keyBy('room_service_code');

        // Get all potential parent records (both from current batch and existing)
        $allParentCodes = array_values($this->parentMapping);
        $parentRecords = AspakServiceRoom::whereIn('room_service_code', $allParentCodes)
            ->get()
            ->keyBy('room_service_code');

        $updateBatch = [];
        $parentErrors = [];

        foreach ($this->parentMapping as $childCode => $parentCode) {
            if (!isset($insertedRecords[$childCode])) {
                continue; // Child record not found (shouldn't happen)
            }

            if (!isset($parentRecords[$parentCode])) {
                $parentErrors[] = [
                    'child_code' => $childCode,
                    'parent_code' => $parentCode,
                    'error' => 'Parent record not found: ' . $parentCode
                ];
                continue;
            }

            $updateBatch[] = [
                'id' => $insertedRecords[$childCode]->id,
                'parent_id' => $parentRecords[$parentCode]->id
            ];
        }

        // Log parent resolution errors
        if (!empty($parentErrors)) {
            Log::warning('AspakRoomImport: Parent resolution errors', $parentErrors);
            $this->errors = array_merge($this->errors, $parentErrors);
        }

        // Batch update parent relationships
        if (!empty($updateBatch)) {
            foreach ($updateBatch as $update) {
                AspakServiceRoom::where('id', $update['id'])
                    ->update(['parent_id' => $update['parent_id']]);
            }

            Log::info('AspakRoomImport: Phase 2 completed', [
                'updated_count' => count($updateBatch)
            ]);
        }
    }

    /**
     * Batch size for memory optimization
     */
    public function batchSize(): int
    {
        return 200;
    }

    /**
     * Validation rules with comprehensive checks
     */
    public function rules(): array
    {
        return [
            '*.kode' => [
                'required',
                'string',
                'max:50',
                Rule::unique('aspak_service_rooms', 'room_service_code')
            ],
            '*.nama' => [
                'required',
                'string',
                'max:255'
            ],
            '*.parent' => [
                'nullable',
                'string',
                'max:50'
            ],
            '*.tree' => [
                'required',
                'in:BRANCH,LEAF'
            ]
        ];
    }

    /**
     * Custom validation messages
     */
    public function customValidationMessages(): array
    {
        return [
            '*.kode.required' => 'Kode ruang layanan wajib diisi.',
            '*.kode.string' => 'Kode ruang layanan harus berupa teks.',
            '*.kode.max' => 'Kode ruang layanan maksimal 50 karakter.',
            '*.kode.unique' => 'Kode ruang layanan sudah terdaftar di database.',
            '*.nama.required' => 'Nama ruang layanan wajib diisi.',
            '*.nama.string' => 'Nama ruang layanan harus berupa teks.',
            '*.nama.max' => 'Nama ruang layanan maksimal 255 karakter.',
            '*.parent.string' => 'Kode parent harus berupa teks.',
            '*.parent.max' => 'Kode parent maksimal 50 karakter.',
            '*.tree.required' => 'Tipe ruang layanan wajib diisi.',
            '*.tree.in' => 'Tipe ruang layanan harus salah satu dari: BRANCH, LEAF.'
        ];
    }

    /**
     * Get all collected errors during the import process
     *
     * Returns an array of errors with detailed information including:
     * - Row numbers where errors occurred
     * - Specific error messages
     * - Error types (validation, database, etc.)
     *
     * @return array Array of error details with row numbers and messages
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

    /**
     * Get the total number of successfully imported records
     *
     * @return int Number of records successfully imported
     */
    public function getSuccessCount(): int
    {
        return $this->successCount;
    }
}
