alter table assets
    add default_image text null after latest_balance;


CREATE TABLE `room_subs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `room_id` bigint NULL,
  `sub_room_code` varchar(64) NULL,
  `sub_room_name` varchar(128) NULL,
  `device_id` varchar(128) NULL,
  `active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL,
  `updated_at` timestamp NULL,
  `created_by` bigint(20) NULL,
  `updated_by` bigint(20) NULL,
  `created_by_name` varchar(128) NULL,
  `updated_by_name` varchar(128) NULL,
  FOREIGN KEY (`room_id`) REFERENCES `rooms` (`id`)
);

create table sensi.aspak_service_rooms
(
    id                bigint auto_increment
        primary key,
    room_service_code varchar(64)             not null,
    room_service_name varchar(128)            null,
    parent_id         bigint                  null,
    tree              enum ('<PERSON><PERSON><PERSON>', 'LEAF') null,
    created_at        timestamp               null,
    updated_at        timestamp               null,
    deleted_at        timestamp               null,
    updated_by        bigint                  null,
    created_by        bigint                  null,
    deleted_by        bigint                  null,
    created_by_name   varchar(128)            null,
    updated_by_name   varchar(128)            null,
    deleted_by_name   varchar(128)            null,
    constraint uk_aspak_service_rooms_code
        unique (room_service_code)
);

create table sensi.aspak_items
(
    id              bigint auto_increment
        primary key,
    item_code       varchar(64)             not null,
    item_name       varchar(128)            null,
    item_synonym    varchar(128)            null,
    parent_id       bigint                  null,
    tree            enum ('BRANCH', 'LEAF') null,
    created_at      timestamp               null,
    updated_at      timestamp               null,
    deleted_at      timestamp               null,
    updated_by      bigint                  null,
    created_by      bigint                  null,
    deleted_by      bigint                  null,
    created_by_name varchar(128)            null,
    updated_by_name varchar(128)            null,
    deleted_by_name varchar(128)            null,
    constraint uk_aspak_item_code
        unique (item_code)
);


