$(document).ready(function() {
    $("#datatable").DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: "/manajemen-aset/kategori-aset-list",
            data: function(d) {
                d.filter = $("#filter").val();
            }
        },
        columns: [
            { data: "category_code", name: "category_code" },
            { data: "category_name", name: "category_name" },
            {
                name: "category_type",
                data: "category_type",
                render: function (v) {
                    const categoryMap = {
                        EQUIPMENT: "Peralatan",
                        LOGISTIC: "Logistik",
                        INFRASTRUCTURE: "Infrastruktur",
                        OTHER: "Lainnya",
                    };
                    return categoryMap[v] || "";
                },
            },
            {
                name: "category_sub_type",
                data: "category_sub_type",
                render: function (v) {
                    const categoryMap = {
                        ALAT_KESEHATAN: "Alat Kesehatan",
                        NON_ALAT_KESEHATAN: "Non Alat Kesehatan",
                    };
                    return categoryMap[v] || "";
                },
            },
            {
                data: "total_asset",
                name: "total_asset",
                orderable: false,
                searchable: false,
            },
            {
                data: "action",
                name: "action",
                orderable: false,
                searchable: false,
                render: function (data, type, row) {
                    var idMatch = data.match(/data-id="([^"]+)"/);
                    var id = idMatch ? idMatch[1] : '';
                    return `
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary-modern btn-modern-rounded dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu datatable-dropdown-menu">
                                    <li><a class="dropdown-item datatable-dropdown-item btn-show" href="javascript:;" data-id="${id}"><i class="fas fa-search me-2"></i>Lihat Detail</a></li>
                                </ul>
                            </div>
                        `;
                }
            },
        ],
        dom: 'rt<"d-flex justify-content-between align-items-center"ip>',
        pageLength: 10,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        language: {
            processing: '<div class="spinner-border spinner-border-sm" role="status"></div> Loading...'
        }
    });

    // Custom search functionality
    $("#searchInput").on("keyup", function () {
        $("#datatable").DataTable().search(this.value).draw();
    });

    // Filter drawer functionality
    $(".datatable-filter-icon").on("click", function (e) {
        e.preventDefault();
        $("#filterDrawer").modal("show");
    });

    // Close filter drawer
    $("#closeFilterDrawer").on("click", function (e) {
        e.preventDefault();
        $("#filterDrawer").modal("hide");
    });

    $("#filterDrawer").on("click", function (e) {
        if (e.target === this) {
            $("#filterDrawer").modal("hide");
        }
    });

    // Custom rows per page functionality
    $("#rowsPerPage").on("change", function () {
        $("#datatable").DataTable().page.len($(this).val()).draw();
    });

    // Update rows per page dropdown when table is drawn
    $("#datatable").on("draw.dt", function () {
        var table = $("#datatable").DataTable();
        var pageLength = table.page.len();
        $("#rowsPerPage").val(pageLength);
    });

    // Apply filter functionality
    $("#applyFilter").on("click", function () {
        $("#datatable").DataTable().ajax.reload();
        $("#filterDrawer").modal("hide");
    });

    // Clear filter functionality
    $("#clearFilter").on("click", function () {
        $("#filter").val("asset");
        $("#datatable").DataTable().ajax.reload();
        $("#filterDrawer").modal("hide");
    });

    function datatableAset(id) {
        if ($.fn.DataTable.isDataTable("#datatable-asset")) {
            $("#datatable-asset").DataTable().clear().destroy();
        }

        let condition = $("#filter_condition").val();

        $("#datatable-asset").DataTable({
            processing: true,
            serverSide: true,
            bDestroy: true,
            ajax: {
                url: `/manajemen-aset/kategori-aset/${id}/aset-list`,
                data: {
                    condition: condition
                }
            },
            columns: [
                {
                    data: "DT_RowIndex",
                    name: "DT_RowIndex",
                    orderable: false,
                    searchable: false,
                },
                { data: "qr_code", name: "qr_code", },
                { data: "item.item_code", name: "item.item_code", },
                { data: "item.item_name", name: "item.item_name", },
                { data: "register_code", name: "register_code", },
                { data: "actual_room", name: "actual_room", },
                { 
                    data: "asset_condition", 
                    name: "asset_condition",
                    render: function(data) {
                        const conditions = {
                            'BAIK': 'Baik',
                            'RUSAK_RINGAN': 'Rusak Ringan',
                            'RUSAK_BERAT': 'Rusak Berat'
                        };
                        return conditions[data] || data;
                    }
                },
            ],
        });
    }

    $("#datatable").on("click", ".btn-show", function () {
        const id = $(this).data("id");

        $.ajax({
            url: `/manajemen-aset/kategori-aset/${id}`,
            type: "GET",
            success: function (response) {
                let category = response.data;

                $("#kode_kategori").text(category.category_code);
                $("#nama_kategori").text(category.category_name);
                $("#tipe_kategori").text(category.category_type);
                $("#jumlah_aset").text(category.total_asset);
                $(".btn-export-excel").data("id", category.id);

                $("#modal-dialog").modal("show");
                datatableAset(id);
            },
        });
    });

    $(document).on("click", ".btn-export-excel", function () {
        const categoryId = $(this).data("id");

        if (!categoryId) {
            alert("Category ID is not available for export.");
            return;
        }

        const exportUrl = `/manajemen-aset/kategori-aset/${categoryId}/export-excel`;

        $.ajax({
            url: exportUrl,
            method: "GET",
            xhrFields: {
                responseType: "blob",
            },
            success: function (blob) {
                const fileName = `Laporan Kategori Aset.xlsx`;
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement("a");
                a.href = url;
                a.download = fileName;
                document.body.appendChild(a);
                a.click();
                a.remove();
                window.URL.revokeObjectURL(url);
            },
            error: function () {
                alert("Failed to export Excel. Please try again.");
            },
        });
    });

    // Tambahkan event listener untuk filter kondisi
    $("#filter_condition").on("change", function() {
        const categoryId = $(".btn-export-excel").data("id");
        if (categoryId) {
            datatableAset(categoryId);
        }
    });
});
