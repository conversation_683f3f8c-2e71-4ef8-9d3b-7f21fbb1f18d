$(document).ready(function() {
    $("#datatable").DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: "/manajemen-aset/posisi-aset-list?guard="+ guard,
            data: function(d) {
                d.category = $("#category").val();
            }
        },
        columns: [
            { data: "room_code", name: "room_code" },
            { data: "room_name", name: "room_name" },
            { data: "building_name", name: "building_name" },
            {
                data: "pic_name",
                name: "pic_name",
                orderable: false,
                searchable: false,
            },
            {
                data: "total_asset",
                name: "total_asset",
                orderable: false,
                searchable: false,
            },
            {
                data: "action",
                name: "action",
                orderable: false,
                searchable: false,
                render: function (data, type, row) {
                    var idMatch = data.match(/data-id="([^"]+)"/);
                    var id = idMatch ? idMatch[1] : '';
                    return `
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary-modern btn-modern-rounded dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu datatable-dropdown-menu">
                                    <li><a class="dropdown-item datatable-dropdown-item btn-show" href="javascript:;" data-id="${id}"><i class="fas fa-search me-2"></i>Lihat Detail</a></li>
                                </ul>
                            </div>
                        `;
                }
            },
        ],
        dom: 'rt<"d-flex justify-content-between align-items-center"ip>',
        pageLength: 10,
        lengthMenu: [[10, 25, 50, 100, 250], [10, 25, 50, 100, 250]],
        language: {
            processing: '<div class="spinner-border spinner-border-sm" role="status"></div> Loading...'
        }
    });

    // Custom search functionality
    $("#searchInput").on("keyup", function () {
        $("#datatable").DataTable().search(this.value).draw();
    });

    // Filter drawer functionality
    $(".datatable-filter-icon").on("click", function (e) {
        e.preventDefault();
        $("#filterDrawer").modal("show");
    });

    // Close filter drawer
    $("#closeFilterDrawer").on("click", function (e) {
        e.preventDefault();
        $("#filterDrawer").modal("hide");
    });

    $("#filterDrawer").on("click", function (e) {
        if (e.target === this) {
            $("#filterDrawer").modal("hide");
        }
    });

    // Custom rows per page functionality
    $("#rowsPerPage").on("change", function () {
        $("#datatable").DataTable().page.len($(this).val()).draw();
    });

    // Update rows per page dropdown when table is drawn
    $("#datatable").on("draw.dt", function () {
        var table = $("#datatable").DataTable();
        var pageLength = table.page.len();
        $("#rowsPerPage").val(pageLength);
    });

    // Apply filter functionality
    $("#applyFilter").on("click", function () {
        $("#datatable").DataTable().ajax.reload();
        $("#filterDrawer").modal("hide");
    });

    // Clear filter functionality
    $("#clearFilter").on("click", function () {
        $("#category").val("all");
        $("#datatable").DataTable().ajax.reload();
        $("#filterDrawer").modal("hide");
    });

    function datatableAset(id) {
        if ($.fn.DataTable.isDataTable("#datatable-asset")) {
            $("#datatable-asset").DataTable().clear().destroy();
        }

        $("#datatable-asset").DataTable({
            processing: true,
            serverSide: true,
            bDestroy: true,
            ajax: `/manajemen-aset/posisi-aset/${id}/aset-list`,
            columns: [
                {
                    data: "DT_RowIndex",
                    name: "DT_RowIndex",
                    orderable: false,
                    searchable: false,
                },
                { data: "qr_code", name: "qr_code" },
                { data: "item_code", name: "items.item_code"},
                { data: "asset_name", name: "asset_name" },
                { data: "register_code", name: "register_code" },
                { data: "brand", name: "asset_entries.brand" },
                { data: "distributor_name", name: "distributors.distributor_name" },
                { data: "unit_price", name: "unit_price", render: $.fn.dataTable.render.number(',', '.', 0) },
            ],
        });
    }

    $("#datatable").on("click", ".btn-show", function () {
        const id = $(this).data("id");

        $.ajax({
            url: `/manajemen-aset/posisi-aset/${id}`,
            type: "GET",
            success: function (response) {
                let room = response.data;

                $("#kode_ruangan").text(room.room_code);
                $("#nama_ruangan").text(room.room_name);
                $("#pic_room").text(room.pic.employee_name || "-");
                $("#jumlah_aset").text(room.assets_count);
                $(".btn-export-excel").data("id", room.id);

                $("#modal-dialog").modal("show");
                datatableAset(id);
            },
        });
    });

    $(document).on("click", ".btn-export-excel", function () {
        const roomId = $(this).data("id");

        if (!roomId) {
            alert("Room ID is not available for export.");
            return;
        }

        const exportUrl = `/manajemen-aset/posisi-aset/${roomId}/export-excel`;

        $.ajax({
            url: exportUrl,
            method: "GET",
            xhrFields: {
                responseType: "blob",
            },
            success: function (blob) {
                const fileName = `Laporan Posisi Aset.xlsx`;
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement("a");
                a.href = url;
                a.download = fileName;
                document.body.appendChild(a);
                a.click();
                a.remove();
                window.URL.revokeObjectURL(url);
            },
            error: function () {
                alert("Failed to export Excel. Please try again.");
            },
        });
    });
});
