$(document).ready(function() {
    $("#datatable").DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: "/manajemen-aset/buku-bantu-list",
            data: function(d) {
                d.document_type = $("#filterDocumentType").val();
                d.date_range = $("#filterDateRange").val();
            }
        },
        columns: [{
            data: "document_code",
            name: "document_code"
        }, {
            data: "document_type",
            name: "document_type"
        }, {
            data: "target_location.room_name",
            name: "target_location.room_name",
            render: function (data, type, row) {
                return data || "-";
            }
        }, {
            data: "action",
            name: "action",
            orderable: false,
            searchable: false,
            render: function (data, type, row) {
                var routeMatch = data.match(/href="([^"]+)"/);
                var route = routeMatch ? routeMatch[1] : '';
                return `
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary-modern btn-modern-rounded dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu datatable-dropdown-menu">
                                <li><a class="dropdown-item datatable-dropdown-item" href="${route}" target="_blank"><i class="fas fa-search me-2"></i>Lihat Detail</a></li>
                            </ul>
                        </div>
                    `;
            }
        }],
        dom: 'rt<"d-flex justify-content-between align-items-center"ip>',
        pageLength: 10,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        language: {
            processing: '<div class="spinner-border spinner-border-sm" role="status"></div> Loading...'
        }
    });

    // Custom search functionality
    $("#searchInput").on("keyup", function () {
        $("#datatable").DataTable().search(this.value).draw();
    });

    // Filter drawer functionality
    $(".datatable-filter-icon").on("click", function (e) {
        e.preventDefault();
        $("#filterDrawer").modal("show");
    });

    // Close filter drawer
    $("#closeFilterDrawer").on("click", function (e) {
        e.preventDefault();
        $("#filterDrawer").modal("hide");
    });

    $("#filterDrawer").on("click", function (e) {
        if (e.target === this) {
            $("#filterDrawer").modal("hide");
        }
    });

    // Custom rows per page functionality
    $("#rowsPerPage").on("change", function () {
        $("#datatable").DataTable().page.len($(this).val()).draw();
    });

    // Update rows per page dropdown when table is drawn
    $("#datatable").on("draw.dt", function () {
        var table = $("#datatable").DataTable();
        var pageLength = table.page.len();
        $("#rowsPerPage").val(pageLength);
    });

    // Initialize date range picker
    $("#filterDateRange").daterangepicker({
        opens: 'left',
        locale: {
            format: 'DD/MM/YYYY'
        }
    });

    // Apply filter functionality
    $("#applyFilter").on("click", function () {
        $("#datatable").DataTable().ajax.reload();
        $("#filterDrawer").modal("hide");
    });

    // Clear filter functionality
    $("#clearFilter").on("click", function () {
        $("#filterDocumentType").val("");
        $("#filterDateRange").val("");
        $("#datatable").DataTable().ajax.reload();
        $("#filterDrawer").modal("hide");
    });
});
