$(document).ready(function() {
    $("#datatable").DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: "/manajemen-aset/perubahan-posisi-list",
            data: function(d) {
                d.ruangan = $("#ruangan").val();
                d.asset = $("#asset").val();
                d.periode = $("#datepicker").val();
            }
        },
        columns: [
            {
                name: "register_code",
                data: "register_code",
            },
            {
                name: "item_name",
                data: "item_name",
                orderable: false,
                searchable: false
            },
            {
                name: "kir_room_name",
                data: "kir_room_name",
                orderable: false,
                searchable: false,
            },
            {
                name: "prev_room_name",
                data: "prev_room_name",
                orderable: false,
                searchable: false,
            },
            {
                name: "room_name",
                data: "room_name",
                orderable: false,
                searchable: false,
            },
            {
                name: "scan_time",
                data: "scan_time",
            },
            {
                data: "action",
                name: "action",
                orderable: false,
                searchable: false,
                render: function (data, type, row) {
                    var idMatch = data.match(/data-id="([^"]+)"/);
                    var id = idMatch ? idMatch[1] : '';
                    return `
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary-modern btn-modern-rounded dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu datatable-dropdown-menu">
                                    <li><a class="dropdown-item datatable-dropdown-item btn-view" href="javascript:;" data-id="${id}"><i class="fas fa-search me-2"></i>Lihat Detail</a></li>
                                </ul>
                            </div>
                        `;
                }
            },
        ],
        dom: 'rt<"d-flex justify-content-between align-items-center"ip>',
        pageLength: 10,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        language: {
            processing: '<div class="spinner-border spinner-border-sm" role="status"></div> Loading...'
        }
    });

    // Custom search functionality
    $("#searchInput").on("keyup", function () {
        $("#datatable").DataTable().search(this.value).draw();
    });

    // Filter drawer functionality
    $(".datatable-filter-icon").on("click", function (e) {
        e.preventDefault();
        $("#filterDrawer").modal("show");
    });

    // Close filter drawer
    $("#closeFilterDrawer").on("click", function (e) {
        e.preventDefault();
        $("#filterDrawer").modal("hide");
    });

    $("#filterDrawer").on("click", function (e) {
        if (e.target === this) {
            $("#filterDrawer").modal("hide");
        }
    });

    // Custom rows per page functionality
    $("#rowsPerPage").on("change", function () {
        $("#datatable").DataTable().page.len($(this).val()).draw();
    });

    // Update rows per page dropdown when table is drawn
    $("#datatable").on("draw.dt", function () {
        var table = $("#datatable").DataTable();
        var pageLength = table.page.len();
        $("#rowsPerPage").val(pageLength);
    });

    // Apply filter functionality
    $("#applyFilter").on("click", function () {
        $("#datatable").DataTable().ajax.reload();
        $("#filterDrawer").modal("hide");
    });

    // Clear filter functionality
    $("#clearFilter").on("click", function () {
        $("#ruangan").val(null).trigger('change');
        $("#asset").val(null).trigger('change');
        $('#datepicker').data('daterangepicker').setStartDate(moment().subtract(3, "months").startOf("day"));
        $('#datepicker').data('daterangepicker').setEndDate(moment());
        $("#datatable").DataTable().ajax.reload();
        $("#filterDrawer").modal("hide");
    });

    // Initialize Select2 for ruangan
    $(".ruangan").select2({
        ajax: {
            url: "/dropdown/room-with-access",
            dataType: "json",
            delay: 250,
            data: function (params) {
                return {
                    q: params.term,
                    page: params.page || 1,
                };
            },
            processResults: function (data, params) {
                params.page = params.page || 1;

                return {
                    results: $.map(data.data, function (item) {
                        return {
                            id: item.id,
                            text: item.room_code + " - " + item.room_name,
                        };
                    }),
                    pagination: {
                        more: data.current_page < data.last_page,
                    },
                };
            },
            cache: true,
        },
        placeholder: "Pilih Ruangan",
        minimumInputLength: 0,
        allowClear: true,
        width: "100%",
        dropdownParent: $("#filterDrawer")
    });

    // Initialize Select2 for asset
    $(".asset").select2({
        ajax: {
            url: "/dropdown/asset",
            dataType: "json",
            delay: 250,
            data: function (params) {
                return {
                    q: params.term,
                    category_type: "EQUIPMENT",
                    page: params.page || 1,
                };
            },
            processResults: function (data, params) {
                params.page = params.page || 1;

                return {
                    results: $.map(data.data, function (item) {
                        return {
                            id: item.id,
                            text: item.qr_code + " - " + item.item_name,
                        };
                    }),
                    pagination: {
                        more: data.current_page < data.last_page,
                    },
                };
            },
            cache: true,
        },
        placeholder: "Pilih Asset",
        minimumInputLength: 0,
        allowClear: true,
        width: "100%",
        dropdownParent: $("#filterDrawer")
    });

    // Initialize date range picker with default 3 months
    $('#datepicker').daterangepicker({
        locale: {format: 'LL'},
        startDate: moment().subtract(3, "months").startOf("day"),
        endDate: moment(),
        ranges: {
            'Hari Ini': [moment(), moment()],
            'Tujuh Hari Kebelakang': [moment().subtract(6, 'days'), moment()],
            'Bulan Sekarang': [moment().startOf('month'), moment().endOf('month')],
            '1 Bulan Lalu': [moment().subtract(1, 'months').startOf('month'), moment().subtract(1, 'months').endOf('month')],
            '2 Bulan Lalu': [moment().subtract(2, 'months').startOf('month'), moment().subtract(2, 'months').endOf('month')],
            '3 Bulan Lalu': [moment().subtract(3, 'months').startOf('month'), moment().subtract(3, 'months').endOf('month')],
            '3 Bulan Berjalan': [moment().subtract(3, 'months').startOf('month'), moment()],
        }
    });

    // Filter type change functionality
    $('input[name="filterType"]').on("change", function () {
        let type = $(this).val();

        if (type == "asset") {
            $("#form-ruangan").addClass("d-none");
            $("#form-asset").removeClass("d-none");
            $("#ruangan").val(null).trigger('change');
        } else {
            $("#form-asset").addClass("d-none");
            $("#form-ruangan").removeClass("d-none");
            $("#asset").val(null).trigger('change');
        }
    });

    // View detail functionality
    $("#datatable").on("click", ".btn-view", function () {
        const id = $(this).data("id");
        // Add your detail view logic here
        console.log("View detail for ID:", id);
    });
});
