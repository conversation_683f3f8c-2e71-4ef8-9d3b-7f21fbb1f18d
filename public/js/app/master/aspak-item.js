$(document).ready(function () {
    // ===== CONSTANTS AND CONFIGURATION =====
    const CONFIG = {
        API_ENDPOINTS: {
            ITEM_LIST: '/master-data/aspak/item/list',
            ITEM_STORE: '/master-data/aspak/item',
            PARENT_BRANCHES: '/master-data/aspak/item/parent-groups'
        },
        DATATABLE: {
            SEARCH_DELAY: 1000,
            DEFAULT_ORDER: [[1, 'asc']],
            DOM_LAYOUT: 'rt<"d-flex justify-content-between align-items-center"ip>',
            PROCESSING_HTML: '<div class="spinner-border spinner-border-sm" role="status"></div> Loading...'
        },
        COLUMNS: {
            INDEX: { width: '5%', className: 'text-center' },
            NAME: { width: '40%' },
            CODE: { width: '15%' },
            TREE: { width: '10%', className: 'text-center' },
            PARENT: { width: '15%', className: 'text-center', visible: false },
            ACTION: { width: '20%', className: 'text-center' }
        },
        TREE: {
            INDENT_PER_LEVEL: 20,
            SCROLL_RESTORE_DELAY: 100
        },
        UI: {
            MODAL_SELECTORS: {
                FORM: '#addDataForm',
                MODAL: '#addDataModal',
                TITLE: '#addDataModalLabel',
                SAVE_BTN: '#saveDataBtn'
            },
            FORM_FIELDS: {
                ITEM_NAME: '#item_name',
                ITEM_CODE: '#item_code',
                TREE: '#tree',
                PARENT_ID: '#parent_id',
                EDIT_ID: '#edit_id',
                METHOD: '#form_method'
            }
        }
    };

    // ===== UTILITY CLASSES =====
    /**
     * Manages button loading states and feedback
     */
    class ButtonStateManager {
        static resetState(buttonElement) {
            const $btn = $(buttonElement);
            const originalHtml = $btn.data('original-html');

            $btn.removeClass('btn-tree-loading btn-outline-success btn-outline-danger')
                .prop('disabled', false)
                .html(originalHtml);
        }
    }

    /**
     * Handles notification display with consistent patterns
     */
    class NotificationHelper {
        static showSuccess(message, title = 'Berhasil!') {
            swal(title, message, 'success');
        }

        static showError(message, title = 'Error!') {
            swal(title, message, 'error');
        }

        static showConfirmDelete(onConfirm) {
            swal({
                title: 'Hapus Data?',
                text: 'Menghapus data bersifat permanen!',
                icon: 'error',
                buttons: {
                    cancel: {
                        text: 'Batal',
                        value: null,
                        visible: true,
                        className: 'btn btn-default',
                        closeModal: true,
                    },
                    confirm: {
                        text: 'Hapus',
                        value: true,
                        visible: true,
                        className: 'btn btn-danger',
                        closeModal: true
                    }
                }
            }).then(onConfirm);
        }
    }

    /**
     * Manages AJAX operations with consistent error handling
     */
    class AjaxManager {
        static performRequest(options) {
            const defaultOptions = {
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                error: function(xhr) {
                    const errorMessage = xhr.responseJSON?.message || 'Terjadi kesalahan pada server';
                    NotificationHelper.showError(errorMessage);
                }
            };

            return $.ajax($.extend(true, defaultOptions, options));
        }

        static loadItemData() {
            return this.performRequest({
                url: CONFIG.API_ENDPOINTS.ITEM_LIST,
                type: 'GET'
            });
        }

        static saveItem(formData, isEdit = false, itemId = null) {
            const url = isEdit ? `${CONFIG.API_ENDPOINTS.ITEM_STORE}/${itemId}` : CONFIG.API_ENDPOINTS.ITEM_STORE;
            const method = isEdit ? 'PUT' : 'POST';

            if (isEdit) {
                formData._method = 'PUT';
            }

            return this.performRequest({
                url: url,
                method: method,
                data: formData
            });
        }

        static deleteItem(route) {
            return this.performRequest({
                url: route,
                type: 'POST',
                data: { _method: 'DELETE' }
            });
        }

        static getItemData(route) {
            return this.performRequest({
                url: route,
                type: 'GET'
            });
        }
    }

    // ===== STATE MANAGEMENT =====
    /**
     * Manages application state with validation and helper methods
     */
    class StateManager {
        constructor() {
            this.itemDataFromServer = [];
            this.itemDataFromServerIndexed = new Map();
            this.itemData = [];
            this.itemDataNested = [];
            this.itemDataNestedFiltered = [];
            this.indexFlatten = 0;
            this.searching = false;
            this.expandedNodes = new Set();
            this.collapsedNodes = new Set();
            this.modalMode = 'add';
            this.editingId = null;
        }

        resetFlattenIndex() {
            this.indexFlatten = 0;
        }

        setSearching(isSearching) {
            this.searching = isSearching;
        }

        setModalMode(mode, editingId = null) {
            this.modalMode = mode;
            this.editingId = editingId;
        }

        isNodeExpanded(nodeId) {
            return this.expandedNodes.has(nodeId);
        }

        isNodeCollapsed(nodeId) {
            return this.collapsedNodes.has(nodeId);
        }

        toggleNodeState(nodeId) {
            if (this.expandedNodes.has(nodeId)) {
                this.expandedNodes.delete(nodeId);
                this.collapsedNodes.add(nodeId);
            } else {
                this.collapsedNodes.delete(nodeId);
                this.expandedNodes.add(nodeId);
            }
        }

        expandAllNodes(groupNodes) {
            groupNodes.forEach(item => {
                this.expandedNodes.add(item.id);
                this.collapsedNodes.delete(item.id);
            });
        }

        collapseAllNodes(groupNodes) {
            groupNodes.forEach(item => {
                this.collapsedNodes.add(item.id);
                this.expandedNodes.delete(item.id);
            });
        }

        updateItemData(serverData) {
            this.itemDataFromServer = serverData;
            this.itemDataFromServerIndexed.clear();
            this.itemDataFromServer.forEach(item => {
                this.itemDataFromServerIndexed.set(item.id, item);
            });
        }

        captureTreeState() {
            try {
                return {
                    expandedNodes: new Set(this.expandedNodes),
                    collapsedNodes: new Set(this.collapsedNodes),
                    searchTerm: $('#searchInput').val() || '',
                    scrollPosition: $(window).scrollTop() || 0,
                    timestamp: Date.now()
                };
            } catch (error) {
                return {
                    expandedNodes: new Set(),
                    collapsedNodes: new Set(),
                    searchTerm: '',
                    scrollPosition: 0,
                    timestamp: Date.now()
                };
            }
        }

        restoreTreeState(savedState) {
            if (!savedState || typeof savedState !== 'object') {
                return;
            }

            try {
                if (savedState.expandedNodes instanceof Set) {
                    this.expandedNodes = new Set(savedState.expandedNodes);
                }
                if (savedState.collapsedNodes instanceof Set) {
                    this.collapsedNodes = new Set(savedState.collapsedNodes);
                }

                if (savedState.searchTerm && typeof savedState.searchTerm === 'string') {
                    $('#searchInput').val(savedState.searchTerm);
                    if (savedState.searchTerm.trim() !== '') {
                        this.searching = true;
                    }
                }

                if (savedState.scrollPosition && typeof savedState.scrollPosition === 'number') {
                    setTimeout(() => {
                        $(window).scrollTop(savedState.scrollPosition);
                    }, CONFIG.TREE.SCROLL_RESTORE_DELAY);
                }
            } catch (error) {
                if (!(this.expandedNodes instanceof Set)) {
                    this.expandedNodes = new Set();
                }
                if (!(this.collapsedNodes instanceof Set)) {
                    this.collapsedNodes = new Set();
                }
            }
        }
    }

    const state = new StateManager();

    // ===== DATA PROCESSING UTILITIES =====
    /**
     * Utilities for processing and transforming item data
     */
    class DataProcessor {
        /**
         * Determines the hierarchical level of an item
         */
        static calculateItemLevel(item) {
            let level = 1;
            let parentId = item.parent_id;
            while (parentId && state.itemDataFromServerIndexed.has(parentId)) {
                level++;
                parentId = state.itemDataFromServerIndexed.get(parentId)?.parent_id;
            }
            return level;
        }

        /**
         * Recursively builds children hierarchy for a parent item
         */
        static buildChildrenHierarchy(parentId, data) {
            return data
                .filter(item => item.parent_id === parentId)
                .map(item => ({
                    ...item,
                    children: this.buildChildrenHierarchy(item.id, data)
                }));
        }

        /**
         * Creates nested data structure from flat array
         */
        static createNestedDataStructure(data) {
            const rootNodes = data.filter(item => !item.parent_id);
            return rootNodes.map(item => ({
                ...item,
                children: this.buildChildrenHierarchy(item.id, data)
            }));
        }

        /**
         * Processes raw server data into structured format with additional metadata
         */
        static processServerData(serverData) {
            state.updateItemData(serverData);

            state.itemData = state.itemDataFromServer.map((item, index) => ({
                ...item,
                DT_RowIndex: index,
                parent_name: state.itemDataFromServerIndexed.get(item.parent_id)?.item_name || null,
                level: this.calculateItemLevel(item),
                isGroup: item.tree === 'BRANCH',
                isExpanded: state.isNodeExpanded(item.id),
                hasChildren: state.itemDataFromServer.some(child => child.parent_id === item.id)
            }));

            state.itemDataNested = this.createNestedDataStructure(state.itemData);

            // Initialize GROUP nodes as collapsed by default if no existing state
            state.itemData.forEach(item => {
                if (item.isGroup && !state.isNodeExpanded(item.id) && !state.isNodeCollapsed(item.id)) {
                    state.collapsedNodes.add(item.id);
                }
            });
        }

        /**
         * Calculates background color based on nesting level for visual hierarchy
         */
        static calculateRowBackgroundColor(level, isGroup) {
            const colorSchemes = {
                group: ['#f8f9fa', '#f1f3f4', '#e8eaed', '#dadce0', '#ced4da'],
                item: ['#ffffff', '#fafbfc', '#f5f6f7', '#f0f1f2', '#ebecf0']
            };

            const colors = isGroup ? colorSchemes.group : colorSchemes.item;
            const colorIndex = Math.min(level - 1, colors.length - 1);
            return colors[colorIndex];
        }
    }

    /**
     * Handles search operations on nested tree data
     */
    class SearchManager {
        /**
         * Recursively searches through nested data structure
         */
        static performNestedSearch(nestedItems, keyword) {
            const normalizedKeyword = keyword.toLowerCase();
            const results = [];

            const itemMatchesKeyword = (item) =>
                item.item_name.toLowerCase().includes(normalizedKeyword) ||
                item.item_code.toLowerCase().includes(normalizedKeyword);

            for (const item of nestedItems) {
                if (itemMatchesKeyword(item)) {
                    results.push(item);
                } else if (item.children?.length > 0) {
                    const matchingChildren = this.performNestedSearch(item.children, keyword);
                    if (matchingChildren.length > 0) {
                        results.push({
                            ...item,
                            children: matchingChildren
                        });
                    }
                }
            }

            return results;
        }

        /**
         * Expands parent nodes when search results contain child items
         */
        static expandParentNodesForSearchResults(searchResults) {
            const expandNode = (node) => {
                if (node.children && node.children.length > 0) {
                    state.expandedNodes.add(node.id);
                    state.collapsedNodes.delete(node.id);
                    node.children.forEach(expandNode);
                }
            };

            searchResults.forEach(expandNode);
        }

        /**
         * Flattens nested search results for DataTable display
         */
        static flattenSearchResults(nestedResults) {
            const flattened = [];

            const flattenNode = (node) => {
                flattened.push({
                    ...node,
                    isExpanded: state.isNodeExpanded(node.id)
                });

                if (node.children && node.children.length > 0 && state.isNodeExpanded(node.id)) {
                    node.children.forEach(flattenNode);
                }
            };

            nestedResults.forEach(flattenNode);
            return flattened;
        }
    }

    // ===== TREE VIEW MANAGEMENT =====
    /**
     * Manages tree view operations and visual rendering
     */
    class TreeViewManager {
        /**
         * Flattens nested data structure for DataTable display
         */
        static flattenNestedData(nestedData) {
            const flattened = [];

            const flattenNode = (node) => {
                state.indexFlatten++;
                flattened.push({
                    ...node,
                    DT_RowIndex: state.indexFlatten,
                    isExpanded: state.isNodeExpanded(node.id)
                });

                if (node.children && node.children.length > 0 && state.isNodeExpanded(node.id)) {
                    node.children.forEach(flattenNode);
                }
            };

            nestedData.forEach(flattenNode);
            return flattened;
        }

        /**
         * Renders tree structure with proper indentation and controls
         */
        static renderTreeStructure(data, type, row) {
            if (type !== 'display') return data;

            const level = row.level || 1;
            const isGroup = row.isGroup;
            const hasChildren = row.hasChildren;
            const isExpanded = row.isExpanded;
            const indentSize = (level - 1) * CONFIG.TREE.INDENT_PER_LEVEL;

            let html = `<div style="margin-left: ${indentSize}px; display: flex; align-items: center;">`;

            if (isGroup && hasChildren) {
                const iconClass = isExpanded ? 'fa-minus-square' : 'fa-plus-square';
                html += `<i class="fas ${iconClass} tree-toggle me-2" data-id="${row.id}" style="cursor: pointer; color: #007bff;"></i>`;
            } else if (isGroup) {
                html += `<i class="fas fa-folder me-2" style="color: #ffc107;"></i>`;
            } else {
                html += `<i class="fas fa-file me-2" style="color: #6c757d; margin-left: 16px;"></i>`;
            }

            html += `<span>${data}</span></div>`;
            return html;
        }

        /**
         * Applies visual styling based on hierarchy level and type
         */
        static applyRowStyling(row, data) {
            const level = data.level || 1;
            const isGroup = data.isGroup;
            const backgroundColor = DataProcessor.calculateRowBackgroundColor(level, isGroup);

            $(row).css({
                'background-color': backgroundColor,
                'border-left': isGroup ? '3px solid #007bff' : '3px solid transparent'
            });

            if (isGroup) {
                $(row).addClass('group-row');
            } else {
                $(row).addClass('item-row');
            }
        }
    }

    // ===== DATATABLE INITIALIZATION =====
    let dataTable;

    /**
     * Initializes and configures the main DataTable
     */
    function initializeDataTable() {
        dataTable = $('#itemTable').DataTable({
            processing: true,
            serverSide: false,
            searching: false,
            paging: true,
            info: true,
            autoWidth: false,
            responsive: false,
            order: CONFIG.DATATABLE.DEFAULT_ORDER,
            dom: CONFIG.DATATABLE.DOM_LAYOUT,
            language: {
                processing: CONFIG.DATATABLE.PROCESSING_HTML,
                info: 'Menampilkan _START_ sampai _END_ dari _TOTAL_ data',
                infoEmpty: 'Menampilkan 0 sampai 0 dari 0 data',
                infoFiltered: '(disaring dari _MAX_ total data)',
                lengthMenu: 'Tampilkan _MENU_ data per halaman',
                zeroRecords: 'Tidak ada data yang ditemukan',
                emptyTable: 'Tidak ada data tersedia',
                paginate: {
                    first: 'Pertama',
                    last: 'Terakhir',
                    next: 'Selanjutnya',
                    previous: 'Sebelumnya'
                }
            },
            columns: [
                {
                    data: 'DT_RowIndex',
                    name: 'DT_RowIndex',
                    title: 'No',
                    orderable: false,
                    searchable: false,
                    width: CONFIG.COLUMNS.INDEX.width,
                    className: CONFIG.COLUMNS.INDEX.className,
                    render: function(data, type, row) {
                        return type === 'display' ? data : data;
                    }
                },
                {
                    data: 'item_name',
                    name: 'item_name',
                    title: 'Nama Item',
                    width: CONFIG.COLUMNS.NAME.width,
                    render: function(data, type, row) {
                        return TreeViewManager.renderTreeStructure(data, type, row);
                    }
                },
                {
                    data: 'item_code',
                    name: 'item_code',
                    title: 'Kode Item',
                    width: CONFIG.COLUMNS.CODE.width
                },
                {
                    data: 'tree',
                    name: 'tree',
                    title: 'Tipe',
                    width: CONFIG.COLUMNS.TREE.width,
                    className: CONFIG.COLUMNS.TREE.className,
                    render: function(data, type, row) {
                        if (type !== 'display') return data;
                        const badgeClass = data === 'BRANCH' ? 'bg-primary' : 'bg-success';
                        const text = data === 'BRANCH' ? 'Grup' : 'Item';
                        return `<span class="badge ${badgeClass}">${text}</span>`;
                    }
                },
                {
                    data: 'parent_name',
                    name: 'parent_name',
                    title: 'Parent',
                    width: CONFIG.COLUMNS.PARENT.width,
                    className: CONFIG.COLUMNS.PARENT.className,
                    visible: CONFIG.COLUMNS.PARENT.visible,
                    render: function(data, type, row) {
                        return data || '-';
                    }
                },
                {
                    data: 'action',
                    name: 'action',
                    title: 'Aksi',
                    orderable: false,
                    searchable: false,
                    width: CONFIG.COLUMNS.ACTION.width,
                    className: CONFIG.COLUMNS.ACTION.className,
                    render: function(data, type, row) {
                        if (type !== 'display') return data;
                        return `<x-master.aspak-item.action-buttons :row="${JSON.stringify(row).replace(/"/g, '&quot;')}" />`;
                    }
                }
            ],
            createdRow: function(row, data, dataIndex) {
                TreeViewManager.applyRowStyling(row, data);
            },
            drawCallback: function(settings) {
                // Re-bind event handlers after table redraw
                bindTreeToggleEvents();
                bindActionButtonEvents();
            }
        });
    }

    // ===== EVENT HANDLERS =====
    /**
     * Binds tree toggle events for expand/collapse functionality
     */
    function bindTreeToggleEvents() {
        $('.tree-toggle').off('click').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const nodeId = parseInt($(this).data('id'));
            const savedState = state.captureTreeState();

            state.toggleNodeState(nodeId);
            refreshDataTable();

            // Restore state after refresh
            setTimeout(() => {
                state.restoreTreeState(savedState);
            }, 50);
        });
    }

    /**
     * Binds action button events for CRUD operations
     */
    function bindActionButtonEvents() {
        // Add child button
        $('.btn-add-child').off('click').on('click', function(e) {
            e.preventDefault();
            const parentId = $(this).data('id');
            const parentName = $(this).data('name');
            openAddModal(parentId, parentName);
        });

        // Edit button
        $('.btn-edit').off('click').on('click', function(e) {
            e.preventDefault();
            const route = $(this).data('route');
            openEditModal(route);
        });

        // Delete button
        $('.btn-delete').off('click').on('click', function(e) {
            e.preventDefault();
            const route = $(this).data('route');
            confirmDelete(route);
        });
    }

    // ===== MODAL OPERATIONS =====
    /**
     * Opens add modal for creating new items
     */
    function openAddModal(parentId = null, parentName = null) {
        state.setModalMode('add');
        resetForm();

        $(CONFIG.UI.MODAL_SELECTORS.TITLE).text('Tambah Data Item');
        $(CONFIG.UI.FORM_FIELDS.METHOD).val('POST');
        $(CONFIG.UI.FORM_FIELDS.EDIT_ID).val('');

        if (parentId) {
            $(CONFIG.UI.FORM_FIELDS.PARENT_ID).val(parentId).trigger('change');
            $(CONFIG.UI.FORM_FIELDS.TREE).val('LEAF').trigger('change');
        }

        loadParentOptions();
        $(CONFIG.UI.MODAL_SELECTORS.MODAL).modal('show');
    }

    /**
     * Opens edit modal for updating existing items
     */
    function openEditModal(route) {
        state.setModalMode('edit');
        resetForm();

        $(CONFIG.UI.MODAL_SELECTORS.TITLE).text('Edit Data Item');
        $(CONFIG.UI.FORM_FIELDS.METHOD).val('PUT');

        AjaxManager.getItemData(route)
            .done(function(response) {
                if (response.success) {
                    const data = response.data;
                    state.setModalMode('edit', data.id);

                    $(CONFIG.UI.FORM_FIELDS.EDIT_ID).val(data.id);
                    $(CONFIG.UI.FORM_FIELDS.ITEM_NAME).val(data.item_name);
                    $(CONFIG.UI.FORM_FIELDS.ITEM_CODE).val(data.item_code);
                    $(CONFIG.UI.FORM_FIELDS.TREE).val(data.tree).trigger('change');

                    loadParentOptions().then(() => {
                        $(CONFIG.UI.FORM_FIELDS.PARENT_ID).val(data.parent_id).trigger('change');
                    });

                    $(CONFIG.UI.MODAL_SELECTORS.MODAL).modal('show');
                } else {
                    NotificationHelper.showError(response.message || 'Gagal memuat data item');
                }
            })
            .fail(function() {
                NotificationHelper.showError('Gagal memuat data item');
            });
    }

    /**
     * Loads parent options for Select2 dropdown
     */
    function loadParentOptions() {
        return AjaxManager.performRequest({
            url: CONFIG.API_ENDPOINTS.PARENT_BRANCHES,
            type: 'GET'
        }).done(function(response) {
            if (response.success) {
                const $parentSelect = $(CONFIG.UI.FORM_FIELDS.PARENT_ID);
                $parentSelect.empty().append('<option value="">Pilih Parent (Opsional)</option>');

                response.data.forEach(function(item) {
                    $parentSelect.append(`<option value="${item.id}">${item.text}</option>`);
                });
            }
        });
    }

    /**
     * Resets form to initial state
     */
    function resetForm() {
        $(CONFIG.UI.MODAL_SELECTORS.FORM)[0].reset();
        $('.error-message').text('');
        $(CONFIG.UI.FORM_FIELDS.PARENT_ID).val('').trigger('change');
        $(CONFIG.UI.FORM_FIELDS.TREE).val('BRANCH').trigger('change');
    }

    // ===== CRUD OPERATIONS =====
    /**
     * Handles form submission for create/update operations
     */
    function handleFormSubmission() {
        const formData = $(CONFIG.UI.MODAL_SELECTORS.FORM).serialize();
        const isEdit = state.modalMode === 'edit';
        const itemId = $(CONFIG.UI.FORM_FIELDS.EDIT_ID).val();

        const $saveBtn = $(CONFIG.UI.MODAL_SELECTORS.SAVE_BTN);
        const originalText = $saveBtn.html();
        $saveBtn.html('<i class="fas fa-spinner fa-spin me-1"></i>Menyimpan...').prop('disabled', true);

        AjaxManager.saveItem(formData, isEdit, itemId)
            .done(function(response) {
                if (response.success) {
                    NotificationHelper.showSuccess(response.message || 'Data berhasil disimpan');
                    $(CONFIG.UI.MODAL_SELECTORS.MODAL).modal('hide');
                    refreshDataTable();
                } else {
                    NotificationHelper.showError(response.message || 'Gagal menyimpan data');
                }
            })
            .fail(function(xhr) {
                if (xhr.status === 422) {
                    displayValidationErrors(xhr.responseJSON.errors);
                } else {
                    NotificationHelper.showError('Terjadi kesalahan saat menyimpan data');
                }
            })
            .always(function() {
                $saveBtn.html(originalText).prop('disabled', false);
            });
    }

    /**
     * Displays validation errors in form
     */
    function displayValidationErrors(errors) {
        $('.error-message').text('');
        
        Object.keys(errors).forEach(function(field) {
            const errorMessage = errors[field][0];
            $(`.error-${field}`).text(errorMessage);
        });
    }

    /**
     * Confirms and handles delete operations
     */
    function confirmDelete(route) {
        NotificationHelper.showConfirmDelete(function(willDelete) {
            if (willDelete) {
                AjaxManager.deleteItem(route)
                    .done(function(response) {
                        if (response.success) {
                            NotificationHelper.showSuccess(response.message || 'Data berhasil dihapus');
                            refreshDataTable();
                        } else {
                            NotificationHelper.showError(response.message || 'Gagal menghapus data');
                        }
                    })
                    .fail(function() {
                        NotificationHelper.showError('Terjadi kesalahan saat menghapus data');
                    });
            }
        });
    }

    // ===== DATA REFRESH AND SEARCH =====
    /**
     * Refreshes DataTable with current data
     */
    function refreshDataTable() {
        const savedState = state.captureTreeState();

        AjaxManager.loadItemData()
            .done(function(response) {
                if (response.success) {
                    DataProcessor.processServerData(response.data);
                    updateDataTableDisplay();
                    state.restoreTreeState(savedState);
                } else {
                    NotificationHelper.showError(response.message || 'Gagal memuat data');
                }
            })
            .fail(function() {
                NotificationHelper.showError('Gagal memuat data dari server');
            });
    }

    /**
     * Updates DataTable display based on current state
     */
    function updateDataTableDisplay() {
        state.resetFlattenIndex();
        
        let displayData;
        if (state.searching) {
            displayData = TreeViewManager.flattenNestedData(state.itemDataNestedFiltered);
        } else {
            displayData = TreeViewManager.flattenNestedData(state.itemDataNested);
        }

        dataTable.clear().rows.add(displayData).draw();
    }

    /**
     * Handles search functionality with debouncing
     */
    let searchTimeout;
    function handleSearch() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function() {
            const keyword = $('#searchInput').val().trim();
            
            if (keyword === '') {
                state.setSearching(false);
                updateDataTableDisplay();
            } else {
                state.setSearching(true);
                state.itemDataNestedFiltered = SearchManager.performNestedSearch(state.itemDataNested, keyword);
                SearchManager.expandParentNodesForSearchResults(state.itemDataNestedFiltered);
                updateDataTableDisplay();
            }
        }, CONFIG.DATATABLE.SEARCH_DELAY);
    }

    // ===== TREE CONTROL FUNCTIONS =====
    /**
     * Expands all tree nodes
     */
    function expandAllNodes() {
        const groupNodes = state.itemData.filter(item => item.isGroup);
        state.expandAllNodes(groupNodes);
        updateDataTableDisplay();
    }

    /**
     * Collapses all tree nodes
     */
    function collapseAllNodes() {
        const groupNodes = state.itemData.filter(item => item.isGroup);
        state.collapseAllNodes(groupNodes);
        updateDataTableDisplay();
    }

    // ===== EXCEL IMPORT FUNCTIONALITY =====
    /**
     * Handles Excel file import
     */
    function handleExcelImport() {
        const fileInput = $('#excel_file')[0];
        const file = fileInput.files[0];
        
        const validation = validateExcelFile(file);
        if (!validation.isValid) {
            NotificationHelper.showError(validation.message);
            return;
        }

        const formData = new FormData();
        formData.append('excel_file', file);
        formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

        const $submitBtn = $('#btn-import-submit');
        const originalText = $submitBtn.html();
        $submitBtn.html('<i class="fas fa-spinner fa-spin me-1"></i>Mengimport...').prop('disabled', true);

        AjaxManager.performRequest({
            url: '/master-data/aspak/item/import',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false
        })
        .done(function(response) {
            if (response.success) {
                showImportSuccessMessage(response.message || 'Import berhasil!');
                $('#form-import-excel').hide();
                refreshDataTable();
            } else {
                if (response.errors && response.errors.length > 0) {
                    showDetailedImportErrors(response);
                } else {
                    showImportErrorMessage(response.message || 'Import gagal!');
                }
                $('#form-import-excel').hide();
            }
        })
        .fail(function(xhr) {
            if (xhr.status === 422 && xhr.responseJSON?.errors) {
                showDetailedImportErrors(xhr.responseJSON);
            } else {
                showImportErrorMessage('Terjadi kesalahan saat mengimport file');
            }
            $('#form-import-excel').hide();
        })
        .always(function() {
            $submitBtn.html(originalText).prop('disabled', false);
        });
    }

    // ===== EVENT BINDINGS =====
    // Initialize DataTable
    initializeDataTable();

    // Load initial data
    refreshDataTable();

    // Search functionality
    $('#searchInput').on('input', handleSearch);

    // Tree control buttons
    $('#expandAllBtn').on('click', expandAllNodes);
    $('#collapseAllBtn').on('click', collapseAllNodes);

    // Form submission
    $(CONFIG.UI.MODAL_SELECTORS.FORM).on('submit', function(e) {
        e.preventDefault();
        handleFormSubmission();
    });

    // Modal events
    $(CONFIG.UI.MODAL_SELECTORS.MODAL).on('hidden.bs.modal', function() {
        resetForm();
    });

    // Excel import
    $('#btn-import-submit').on('click', function(e) {
        e.preventDefault();
        handleExcelImport();
    });

    // Download template
    $('#btn-download-template').on('click', function(e) {
        e.preventDefault();
        const originalText = $(this).html();
        $(this).html('<i class="fas fa-spinner fa-spin me-1"></i>Downloading...').prop('disabled', true);
        
        const link = document.createElement('a');
        link.href = '/master-data/aspak/item/download-template';
        link.download = 'template_aspak_item.xlsx';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        setTimeout(() => {
            $(this).html(originalText).prop('disabled', false);
        }, 1000);
    });

    // Reset form when modal is hidden
    $('#modal-import-excel').on('hidden.bs.modal', function() {
        $('#form-import-excel')[0].reset();
        $('.error-message').text('');
        $('#btn-import-submit').html('<i class="fas fa-upload me-1"></i>Import').prop('disabled', false);
        resetImportMessageArea();
        $('#form-import-excel').show();
    });

    // ===== IMPORT HELPER FUNCTIONS =====
    /**
     * Enhanced Excel file validation with MIME type and file signature checking
     */
    function validateExcelFile(file) {
        if (!file) {
            return {
                isValid: false,
                message: 'Silakan pilih file Excel terlebih dahulu.'
            };
        }

        const allowedExtensions = ['.xlsx', '.xls'];
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        if (!allowedExtensions.includes(fileExtension)) {
            return {
                isValid: false,
                message: 'Format file tidak didukung. Gunakan file Excel (.xlsx atau .xls) yang valid.'
            };
        }

        const allowedMimeTypes = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel',
            'application/excel',
            'application/x-excel',
            'application/x-msexcel'
        ];
        
        if (file.type && !allowedMimeTypes.includes(file.type)) {
            return {
                isValid: false,
                message: 'File yang dipilih bukan file Excel yang valid. Pastikan file memiliki format .xlsx atau .xls yang benar.'
            };
        }

        const maxSize = 5 * 1024 * 1024;
        if (file.size > maxSize) {
            return {
                isValid: false,
                message: 'Ukuran file terlalu besar. Maksimal 5MB.'
            };
        }

        if (file.size < 100) {
            return {
                isValid: false,
                message: 'File terlalu kecil atau kosong. Pastikan file Excel berisi data yang valid.'
            };
        }

        return {
            isValid: true,
            message: 'File valid'
        };
    }

    /**
     * Reset import message area to show form
     */
    function resetImportMessageArea() {
        $('#import-message-area').addClass('d-none');
        $('#import-success-message').addClass('d-none');
        $('#import-error-message').addClass('d-none');
        $('#form-import-excel').show();
    }

    /**
     * Show import success message
     */
    function showImportSuccessMessage(message) {
        $('#success-text').text(message);
        $('#import-success-message').removeClass('d-none');
        $('#import-error-message').addClass('d-none');
        $('#import-message-area').removeClass('d-none');
    }

    /**
     * Show import error message
     */
    function showImportErrorMessage(message, details = null) {
        $('#error-text').text(message);
        if (details) {
            $('#error-details').html(details);
        }
        $('#import-error-message').removeClass('d-none');
        $('#import-success-message').addClass('d-none');
        $('#import-message-area').removeClass('d-none');
    }

    /**
     * Show detailed import errors with hierarchical data context
     */
    function showDetailedImportErrors(response) {
        const errorsByRow = {};

        response.errors.forEach(function(error) {
            const rowNumber = error.row;
            if (!errorsByRow[rowNumber]) {
                errorsByRow[rowNumber] = {
                    values: error.values || {},
                    errors: []
                };
            }
            errorsByRow[rowNumber].errors.push({
                attribute: error.attribute,
                messages: error.errors
            });
        });

        let errorHtml = `
            <div class="text-start">
                <p><strong>Total Error:</strong> ${response.error_count || response.errors.length}</p>
                <div style="max-height: 400px; overflow-y: auto;">
        `;

        const sortedRows = Object.keys(errorsByRow).sort((a, b) => parseInt(a) - parseInt(b));

        sortedRows.forEach(function(rowNumber) {
            const rowData = errorsByRow[rowNumber];
            const values = rowData.values;

            errorHtml += `
                <div class="card mb-3 border-danger">
                    <div class="card-header bg-danger text-white">
                        <h6 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Baris ${rowNumber}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-danger mb-2">Error yang ditemukan:</h6>
                                <ul class="list-unstyled mb-0">
            `;

            rowData.errors.forEach(function(errorItem) {
                errorHtml += `
                                    <li class="mb-1">
                                        <strong class="text-primary">${errorItem.attribute}:</strong>
                                        <span class="text-danger">${errorItem.messages.join(', ')}</span>
                                    </li>
                `;
            });

            errorHtml += `
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-info mb-2">Data pada baris ini:</h6>
                                <div class="small">
                                    <div class="mb-1"><strong>Kode:</strong> ${values.kode || '-'}</div>
                                    <div class="mb-1"><strong>Nama:</strong> ${values.nama || '-'}</div>
                                    <div class="mb-1"><strong>Parent:</strong> ${values.parent || '-'}</div>
                                    <div class="mb-1"><strong>Tree:</strong> ${values.tree || '-'}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        errorHtml += `
                </div>
            </div>
        `;

        showImportErrorMessage('Import Gagal! Terdapat error pada data yang diimport.', errorHtml);
    }
});