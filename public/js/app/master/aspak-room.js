$(document).ready(function () {
    // ===== CONSTANTS AND CONFIGURATION =====
    const CONFIG = {
        API_ENDPOINTS: {
            ROOM_LIST: '/master-data/aspak/room/list',
            ROOM_STORE: '/master-data/aspak/room',
            PARENT_BRANCHES: '/master-data/aspak/room/parent-groups'
        },
        DATATABLE: {
            SEARCH_DELAY: 1000,
            DEFAULT_ORDER: [[1, 'asc']],
            DOM_LAYOUT: 'rt<"d-flex justify-content-between align-items-center"ip>',
            PROCESSING_HTML: '<div class="spinner-border spinner-border-sm" role="status"></div> Loading...'
        },
        COLUMNS: {
            INDEX: { width: '5%', className: 'text-center' },
            NAME: { width: '40%' },
            CODE: { width: '15%' },
            TREE: { width: '10%', className: 'text-center' },
            PARENT: { width: '15%', className: 'text-center', visible: false },
            ACTION: { width: '20%', className: 'text-center' }
        },
        TREE: {
            INDENT_PER_LEVEL: 20,
            SCROLL_RESTORE_DELAY: 100
        },
        UI: {
            MODAL_SELECTORS: {
                FORM: '#addDataForm',
                MODAL: '#addDataModal',
                TITLE: '#addDataModalLabel',
                SAVE_BTN: '#saveDataBtn'
            },
            FORM_FIELDS: {
                ROOM_NAME: '#room_service_name',
                ROOM_CODE: '#room_service_code',
                TREE: '#tree',
                PARENT_ID: '#parent_id',
                EDIT_ID: '#edit_id',
                METHOD: '#form_method'
            }
        }
    };

    // ===== UTILITY CLASSES =====
    /**
     * Manages button loading states and feedback
     */
    class ButtonStateManager {
        static resetState(buttonElement) {
            const $btn = $(buttonElement);
            const originalHtml = $btn.data('original-html');

            $btn.removeClass('btn-tree-loading btn-outline-success btn-outline-danger')
                .prop('disabled', false)
                .html(originalHtml);
        }
    }

    /**
     * Handles notification display with consistent patterns
     */
    class NotificationHelper {
        static showSuccess(message, title = 'Berhasil!') {
            swal(title, message, 'success');
        }

        static showError(message, title = 'Error!') {
            swal(title, message, 'error');
        }

        static showConfirmDelete(onConfirm) {
            swal({
                title: 'Hapus Data?',
                text: 'Menghapus data bersifat permanen!',
                icon: 'error',
                buttons: {
                    cancel: {
                        text: 'Batal',
                        value: null,
                        visible: true,
                        className: 'btn btn-default',
                        closeModal: true,
                    },
                    confirm: {
                        text: 'Hapus',
                        value: true,
                        visible: true,
                        className: 'btn btn-danger',
                        closeModal: true
                    }
                }
            }).then(onConfirm);
        }
    }

    /**
     * Manages AJAX operations with consistent error handling
     */
    class AjaxManager {
        static performRequest(options) {
            const defaultOptions = {
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                error: function(xhr) {
                    const errorMessage = xhr.responseJSON?.message || 'Terjadi kesalahan pada server';
                    NotificationHelper.showError(errorMessage);
                }
            };

            return $.ajax($.extend(true, defaultOptions, options));
        }

        static loadRoomData() {
            return this.performRequest({
                url: CONFIG.API_ENDPOINTS.ROOM_LIST,
                type: 'GET'
            });
        }

        static saveRoom(formData, isEdit = false, roomId = null) {
            const url = isEdit ? `${CONFIG.API_ENDPOINTS.ROOM_STORE}/${roomId}` : CONFIG.API_ENDPOINTS.ROOM_STORE;
            const method = isEdit ? 'PUT' : 'POST';

            if (isEdit) {
                formData._method = 'PUT';
            }

            return this.performRequest({
                url: url,
                method: method,
                data: formData
            });
        }

        static deleteRoom(route) {
            return this.performRequest({
                url: route,
                type: 'POST',
                data: { _method: 'DELETE' }
            });
        }

        static getRoomData(route) {
            return this.performRequest({
                url: route,
                type: 'GET'
            });
        }
    }

    // ===== STATE MANAGEMENT =====
    /**
     * Manages application state with validation and helper methods
     */
    class StateManager {
        constructor() {
            this.roomDataFromServer = [];
            this.roomDataFromServerIndexed = new Map();
            this.roomData = [];
            this.roomDataNested = [];
            this.roomDataNestedFiltered = [];
            this.indexFlatten = 0;
            this.searching = false;
            this.expandedNodes = new Set();
            this.collapsedNodes = new Set();
            this.modalMode = 'add';
            this.editingId = null;
        }

        resetFlattenIndex() {
            this.indexFlatten = 0;
        }

        setSearching(isSearching) {
            this.searching = isSearching;
        }

        setModalMode(mode, editingId = null) {
            this.modalMode = mode;
            this.editingId = editingId;
        }

        isNodeExpanded(nodeId) {
            return this.expandedNodes.has(nodeId);
        }

        isNodeCollapsed(nodeId) {
            return this.collapsedNodes.has(nodeId);
        }

        toggleNodeState(nodeId) {
            if (this.expandedNodes.has(nodeId)) {
                this.expandedNodes.delete(nodeId);
                this.collapsedNodes.add(nodeId);
            } else {
                this.collapsedNodes.delete(nodeId);
                this.expandedNodes.add(nodeId);
            }
        }

        expandAllNodes(groupNodes) {
            groupNodes.forEach(item => {
                this.expandedNodes.add(item.id);
                this.collapsedNodes.delete(item.id);
            });
        }

        collapseAllNodes(groupNodes) {
            groupNodes.forEach(item => {
                this.collapsedNodes.add(item.id);
                this.expandedNodes.delete(item.id);
            });
        }

        updateRoomData(serverData) {
            this.roomDataFromServer = serverData;
            this.roomDataFromServerIndexed.clear();
            this.roomDataFromServer.forEach(item => {
                this.roomDataFromServerIndexed.set(item.id, item);
            });
        }

        captureTreeState() {
            try {
                return {
                    expandedNodes: new Set(this.expandedNodes),
                    collapsedNodes: new Set(this.collapsedNodes),
                    searchTerm: $('#searchInput').val() || '',
                    scrollPosition: $(window).scrollTop() || 0,
                    timestamp: Date.now()
                };
            } catch (error) {
                return {
                    expandedNodes: new Set(),
                    collapsedNodes: new Set(),
                    searchTerm: '',
                    scrollPosition: 0,
                    timestamp: Date.now()
                };
            }
        }

        restoreTreeState(savedState) {
            if (!savedState || typeof savedState !== 'object') {
                return;
            }

            try {
                if (savedState.expandedNodes instanceof Set) {
                    this.expandedNodes = new Set(savedState.expandedNodes);
                }
                if (savedState.collapsedNodes instanceof Set) {
                    this.collapsedNodes = new Set(savedState.collapsedNodes);
                }

                if (savedState.searchTerm && typeof savedState.searchTerm === 'string') {
                    $('#searchInput').val(savedState.searchTerm);
                    if (savedState.searchTerm.trim() !== '') {
                        this.searching = true;
                    }
                }

                if (savedState.scrollPosition && typeof savedState.scrollPosition === 'number') {
                    setTimeout(() => {
                        $(window).scrollTop(savedState.scrollPosition);
                    }, CONFIG.TREE.SCROLL_RESTORE_DELAY);
                }
            } catch (error) {
                if (!(this.expandedNodes instanceof Set)) {
                    this.expandedNodes = new Set();
                }
                if (!(this.collapsedNodes instanceof Set)) {
                    this.collapsedNodes = new Set();
                }
            }
        }
    }

    const state = new StateManager();

    // ===== DATA PROCESSING UTILITIES =====
    /**
     * Utilities for processing and transforming room data
     */
    class DataProcessor {
        /**
         * Determines the hierarchical level of a room item
         */
        static calculateItemLevel(item) {
            let level = 1;
            let parentId = item.parent_id;
            while (parentId && state.roomDataFromServerIndexed.has(parentId)) {
                level++;
                parentId = state.roomDataFromServerIndexed.get(parentId)?.parent_id;
            }
            return level;
        }

        /**
         * Recursively builds children hierarchy for a parent item
         */
        static buildChildrenHierarchy(parentId, data) {
            return data
                .filter(item => item.parent_id === parentId)
                .map(item => ({
                    ...item,
                    children: this.buildChildrenHierarchy(item.id, data)
                }));
        }

        /**
         * Creates nested data structure from flat array
         */
        static createNestedDataStructure(data) {
            const rootNodes = data.filter(item => !item.parent_id);
            return rootNodes.map(item => ({
                ...item,
                children: this.buildChildrenHierarchy(item.id, data)
            }));
        }

        /**
         * Processes raw server data into structured format with additional metadata
         */
        static processServerData(serverData) {
            state.updateRoomData(serverData);

            state.roomData = state.roomDataFromServer.map((item, index) => ({
                ...item,
                DT_RowIndex: index,
                parent_name: state.roomDataFromServerIndexed.get(item.parent_id)?.room_service_name || null,
                level: this.calculateItemLevel(item),
                isGroup: item.tree === 'BRANCH',
                isExpanded: state.isNodeExpanded(item.id),
                hasChildren: state.roomDataFromServer.some(child => child.parent_id === item.id)
            }));

            state.roomDataNested = this.createNestedDataStructure(state.roomData);

            // Initialize GROUP nodes as collapsed by default if no existing state
            state.roomData.forEach(item => {
                if (item.isGroup && !state.isNodeExpanded(item.id) && !state.isNodeCollapsed(item.id)) {
                    state.collapsedNodes.add(item.id);
                }
            });
        }

        /**
         * Calculates background color based on nesting level for visual hierarchy
         */
        static calculateRowBackgroundColor(level, isGroup) {
            const colorSchemes = {
                group: ['#f8f9fa', '#f1f3f4', '#e8eaed', '#dadce0', '#ced4da'],
                room: ['#ffffff', '#fafbfc', '#f5f6f7', '#f0f1f2', '#ebecf0']
            };

            const colors = isGroup ? colorSchemes.group : colorSchemes.room;
            const colorIndex = Math.min(level - 1, colors.length - 1);
            return colors[colorIndex];
        }
    }

    /**
     * Handles search operations on nested tree data
     */
    class SearchManager {
        /**
         * Recursively searches through nested data structure
         */
        static performNestedSearch(nestedRooms, keyword) {
            const normalizedKeyword = keyword.toLowerCase();
            const results = [];

            const itemMatchesKeyword = (item) =>
                item.room_service_name.toLowerCase().includes(normalizedKeyword) ||
                item.room_service_code.toLowerCase().includes(normalizedKeyword);

            for (const room of nestedRooms) {
                if (itemMatchesKeyword(room)) {
                    results.push(room);
                } else if (room.children?.length > 0) {
                    const matchingChildren = this.performNestedSearch(room.children, keyword);
                    if (matchingChildren.length > 0) {
                        results.push({
                            ...room,
                            children: matchingChildren
                        });
                    }
                }
            }

            return results;
        }

        /**
         * Expands parent nodes when search results contain child items
         */
        static expandParentNodesForSearchResults(searchResults) {
            if (!state.searching) return;

            const expandParentsRecursively = (items) => {
                for (const item of items) {
                    if (item.isGroup && item.children?.length > 0) {
                        state.expandedNodes.add(item.id);
                        state.collapsedNodes.delete(item.id);
                        expandParentsRecursively(item.children);
                    }
                }
            };

            expandParentsRecursively(searchResults);
        }
    }

    /**
     * Handles sorting operations on nested tree data
     */
    class SortingManager {
        /**
         * Recursively sorts nested data by specified column
         */
        static sortNestedDataByColumn(nestedRooms, columnName, direction) {
            const sortedResults = [...nestedRooms];

            // Sort current level
            sortedResults.sort((a, b) => {
                const comparison = a[columnName] > b[columnName] ? 1 : -1;
                return direction === 'asc' ? comparison : -comparison;
            });

            // Recursively sort children
            sortedResults.forEach(item => {
                if (item.children?.length > 0) {
                    item.children = this.sortNestedDataByColumn(item.children, columnName, direction);
                }
            });

            return sortedResults;
        }
    }

    /**
     * Handles flattening of nested data for display purposes
     */
    class TreeFlattener {
        /**
         * Flattens nested data structure for DataTable display
         */
        static flattenNestedDataForDisplay(nestedRooms, respectCollapse = true) {
            let results = [];

            for (const item of nestedRooms) {
                const flatItem = {
                    ...item,
                    DT_RowIndex: ++state.indexFlatten,
                    isExpanded: state.isNodeExpanded(item.id),
                    backgroundColor: DataProcessor.calculateRowBackgroundColor(item.level, item.isGroup),
                    levelClass: `tree-level-${item.level}`,
                    typeClass: item.isGroup ? 'group-row' : 'room-row'
                };
                results.push(flatItem);

                // Only show children if parent is expanded or not respecting collapse state
                if (item.children?.length > 0) {
                    const shouldShowChildren = !respectCollapse || !item.isGroup || state.isNodeExpanded(item.id);
                    if (shouldShowChildren) {
                        results = results.concat(this.flattenNestedDataForDisplay(item.children, respectCollapse));
                    }
                }
            }

            return results;
        }

        /**
         * Enhances flat data with search highlighting
         */
        static enhanceDataWithSearchHighlighting(flattenedData, searchTerm = '') {
            const hasSearchTerm = searchTerm.trim() !== '';
            const normalizedSearchTerm = searchTerm.toLowerCase();

            return flattenedData.map(item => ({
                ...item,
                isSearchMatch: hasSearchTerm && (
                    item.room_service_name.toLowerCase().includes(normalizedSearchTerm) ||
                    item.room_service_code.toLowerCase().includes(normalizedSearchTerm)
                )
            }));
        }
    }

    // ===== DATA PROCESSING PIPELINE =====
    /**
     * Processes and filters data for DataTable display
     */
    function processAndFilterDataForTable(payload, callback) {
        state.resetFlattenIndex();

        // Apply search filter
        const searchTerm = payload.search.value.trim().toLowerCase();

        if (searchTerm) {
            state.roomDataNestedFiltered = SearchManager.performNestedSearch(state.roomDataNested, searchTerm);
            SearchManager.expandParentNodesForSearchResults(state.roomDataNestedFiltered);
            state.setSearching(false);
        } else {
            state.roomDataNestedFiltered = state.roomDataNested;
        }

        // Apply sorting
        const orderColumn = payload.order[0];
        const columnName = payload.columns[orderColumn.column].data;
        state.roomDataNestedFiltered = SortingManager.sortNestedDataByColumn(
            state.roomDataNestedFiltered,
            columnName,
            orderColumn.dir
        );

        // Flatten for display
        const flattenedData = TreeFlattener.flattenNestedDataForDisplay(state.roomDataNestedFiltered);

        // Enhance with search highlighting
        const enhancedData = TreeFlattener.enhanceDataWithSearchHighlighting(flattenedData, searchTerm);

        callback({
            draw: payload.draw,
            data: enhancedData,
            recordsTotal: state.roomData.length,
            recordsFiltered: enhancedData.length,
        });
    }

    /**
     * Handles AJAX data loading with error handling
     */
    function handleDataTableLoad(payload, callback) {
        if (state.roomDataFromServer.length === 0) {
            AjaxManager.loadRoomData()
                .done(function (response) {
                    DataProcessor.processServerData(response.data);
                    processAndFilterDataForTable(payload, callback);
                })
                .fail(function () {
                    callback({
                        draw: payload.draw,
                        data: [],
                        recordsTotal: 0,
                        recordsFiltered: 0,
                    });
                });
        } else {
            processAndFilterDataForTable(payload, callback);
        }
    }

    // ===== TREE VIEW UTILITIES =====
    /**
     * Gets the appropriate icon for a tree node
     */
    function getTreeNodeIcon(item) {
        if (!item.isGroup) {
            return '<i class="fas fa-file text-muted me-2"></i>';
        }

        if (!item.hasChildren) {
            return '<i class="fas fa-folder text-warning me-2"></i>';
        }

        const isExpanded = state.isNodeExpanded(item.id);
        const iconClass = isExpanded ? 'fa-folder-open' : 'fa-folder';
        const expandIcon = isExpanded ? 'fa-minus-square' : 'fa-plus-square';

        return `<span class="tree-toggle" data-id="${item.id}" style="cursor: pointer;">
                    <i class="fas ${expandIcon} text-primary me-1"></i>
                </span>
                <i class="fas ${iconClass} text-warning me-2"></i>`;
    }

    /**
     * Gets indentation HTML based on hierarchical level
     */
    function getTreeIndentation(level) {
        const indentWidth = (level - 1) * CONFIG.TREE.INDENT_PER_LEVEL;
        return `<span style="display: inline-block; width: ${indentWidth}px;"></span>`;
    }

    /**
     * Toggles expand/collapse state of a tree node
     */
    function toggleTreeNode(nodeId) {
        state.toggleNodeState(nodeId);
        table.ajax.reload(null, false);
    }

    /**
     * Expands all GROUP nodes in the tree
     */
    function expandAllTreeNodes() {
        const groupNodes = state.roomData.filter(item => item.isGroup && item.hasChildren);
        state.expandAllNodes(groupNodes);
        table.ajax.reload(null, false);
    }

    /**
     * Collapses all GROUP nodes in the tree
     */
    function collapseAllTreeNodes() {
        const groupNodes = state.roomData.filter(item => item.isGroup && item.hasChildren);
        state.collapseAllNodes(groupNodes);
        table.ajax.reload(null, false);
    }

    // ===== MODAL AND FORM MANAGEMENT =====
    /**
     * Manages modal operations and form handling
     */
    class ModalManager {
        /**
         * Initialize Select2 for parent dropdown
         */
        static initializeParentSelect2() {
            $(CONFIG.UI.FORM_FIELDS.PARENT_ID).select2({
                placeholder: 'Pilih Parent',
                allowClear: true,
                ajax: {
                    url: CONFIG.API_ENDPOINTS.PARENT_GROUPS,
                    dataType: 'json',
                    delay: 250,
                    data: function (params) {
                        return {
                            q: params.term,
                            page: params.page || 1
                        };
                    },
                    processResults: function (data, params) {
                        params.page = params.page || 1;
                        return {
                            results: data.data,
                            pagination: {
                                more: (params.page * 10) < data.total
                            }
                        };
                    },
                    cache: true
                },
                minimumInputLength: 0,
                dropdownParent: $(CONFIG.UI.MODAL_SELECTORS.MODAL),
                templateResult: function(data) {
                    if (!data.id) {
                        return data.text;
                    }
                    return $('<span>' + data.text + '</span>');
                },
                templateSelection: function(data) {
                    return data.text || data.text;
                }
            });
        }

        /**
         * Reset form and clear validation errors
         */
        static resetForm(mode = 'add') {
            $(CONFIG.UI.MODAL_SELECTORS.FORM)[0].reset();
            $('.is-invalid').removeClass('is-invalid');
            $('.invalid-feedback').text('');
            $('#parentGroup').show();
            $(CONFIG.UI.FORM_FIELDS.PARENT_ID).val(null);

            state.setModalMode(mode);

            // Update modal title and button text based on mode
            if (mode === 'edit') {
                $(CONFIG.UI.MODAL_SELECTORS.TITLE).text('Edit Data Ruangan');
                $(CONFIG.UI.MODAL_SELECTORS.SAVE_BTN).text('Perbarui');
                $(CONFIG.UI.FORM_FIELDS.METHOD).val('PUT');
            } else {
                $(CONFIG.UI.MODAL_SELECTORS.TITLE).text('Tambah Data Ruangan');
                $(CONFIG.UI.MODAL_SELECTORS.SAVE_BTN).text('Simpan');
                $(CONFIG.UI.FORM_FIELDS.METHOD).val('POST');
            }

            $(CONFIG.UI.FORM_FIELDS.EDIT_ID).val('');
            $(CONFIG.UI.FORM_FIELDS.PARENT_ID).closest('.mb-3').find('label[for="parent_id"]')
                .html('Parent <span class="optional-text">(opsional)</span>');
            $(CONFIG.UI.FORM_FIELDS.PARENT_ID).prop('required', false);
        }

        /**
         * Show validation errors on form fields
         */
        static showValidationErrors(errors) {
            $('.is-invalid').removeClass('is-invalid');
            $('.invalid-feedback').text('');

            $.each(errors, function (field, messages) {
                $('#' + field).addClass('is-invalid');
                $('#' + field + '_error').text(messages[0]);
            });
        }

        /**
         * Populate form with existing data for editing
         */
        static populateFormForEdit(data) {
            this.resetForm('edit');
            state.setModalMode('edit', data.id);

            $(CONFIG.UI.FORM_FIELDS.EDIT_ID).val(data.id);
            $(CONFIG.UI.FORM_FIELDS.ROOM_NAME).val(data.room_service_name);
            $(CONFIG.UI.FORM_FIELDS.ROOM_CODE).val(data.room_service_code);
            $(CONFIG.UI.FORM_FIELDS.TREE).val(data.tree).trigger('change');

            // Handle parent selection
            if (data.parent_id && data.parent_name) {
                const parentOption = new Option(data.parent_name, data.parent_id, true, true);
                $(CONFIG.UI.FORM_FIELDS.PARENT_ID).append(parentOption).trigger('change');
            } else {
                $(CONFIG.UI.FORM_FIELDS.PARENT_ID).val(null).trigger('change');
            }
        }

        /**
         * Submit form data via AJAX
         */
        static submitForm() {
            const savedTreeState = state.captureTreeState();

            const formData = {
                room_service_name: $(CONFIG.UI.FORM_FIELDS.ROOM_NAME).val(),
                room_service_code: $(CONFIG.UI.FORM_FIELDS.ROOM_CODE).val(),
                tree: $(CONFIG.UI.FORM_FIELDS.TREE).val(),
                parent_id: $(CONFIG.UI.FORM_FIELDS.PARENT_ID).val() || null
            };

            const isEdit = state.modalMode === 'edit' && state.editingId;
            const originalButtonText = $(CONFIG.UI.MODAL_SELECTORS.SAVE_BTN).text();
            const loadingText = isEdit ? 'Memperbarui...' : 'Menyimpan...';
            const successMessage = isEdit ? 'Data berhasil diperbarui' : 'Data berhasil disimpan';

            // Show loading state
            $(CONFIG.UI.MODAL_SELECTORS.SAVE_BTN).prop('disabled', true).text(loadingText);

            AjaxManager.saveRoom(formData, isEdit, state.editingId)
                .done(function(response) {
                    ModalManager.resetForm();
                    $(CONFIG.UI.MODAL_SELECTORS.MODAL).modal('hide');

                    // Force reload DataTable without cache
                    state.roomDataFromServer = [];

                    table.ajax.reload(function() {
                        state.restoreTreeState(savedTreeState);
                    }, false);

                    NotificationHelper.showSuccess(response.message || successMessage);
                })
                .fail(function(xhr) {
                    if (xhr.status === 422) {
                        ModalManager.showValidationErrors(xhr.responseJSON.errors);
                    } else {
                        const errorMessage = xhr.responseJSON?.message || 'Terjadi kesalahan saat menyimpan data';
                        NotificationHelper.showError(errorMessage);
                    }
                    state.restoreTreeState(savedTreeState);
                })
                .always(function() {
                    $(CONFIG.UI.MODAL_SELECTORS.SAVE_BTN).prop('disabled', false).text(originalButtonText);
                });
        }
    }

    // ===== DATATABLE CONFIGURATION =====
    const dataTableConfig = {
        serverSide: true,
        processing: true,
        paging: false,
        searchDelay: CONFIG.DATATABLE.SEARCH_DELAY,
        ajax: handleDataTableLoad,
        columns: [
            {
                data: 'DT_RowIndex',
                name: 'DT_RowIndex',
                orderable: false,
                searchable: false,
                width: CONFIG.COLUMNS.INDEX.width,
                className: CONFIG.COLUMNS.INDEX.className,
                visible: false,
            },
            {
                data: 'room_service_name',
                name: 'room_service_name',
                width: CONFIG.COLUMNS.NAME.width,
                render: function (data, type, row) {
                    if (type === 'display') {
                        const indentation = getTreeIndentation(row.level);
                        const icon = getTreeNodeIcon(row);
                        return `${indentation}${icon}${data}`;
                    }
                    return data;
                }
            },
            {
                data: 'room_service_code',
                name: 'room_service_code',
                width: CONFIG.COLUMNS.CODE.width
            },
            {
                data: 'tree',
                name: 'tree',
                orderable: false,
                searchable: false,
                width: CONFIG.COLUMNS.TREE.width,
                className: CONFIG.COLUMNS.TREE.className,
                render: function (data, type, row) {
                    if (type === 'display') {
                        const badgeClass = data === 'BRANCH' ? 'bg-primary' : 'bg-success';
                        const badgeText = data === 'BRANCH' ? 'Branch' : 'Leaf';
                        return `<span class="badge ${badgeClass}">${badgeText}</span>`;
                    }
                    return data;
                }
            },
            {
                data: 'parent_name',
                name: 'parent_name',
                orderable: false,
                searchable: false,
                width: CONFIG.COLUMNS.PARENT.width,
                className: CONFIG.COLUMNS.PARENT.className,
                visible: CONFIG.COLUMNS.PARENT.visible,
            },
            {
                name: 'action',
                data: 'action_buttons',
                orderable: false,
                searchable: false,
                width: CONFIG.COLUMNS.ACTION.width,
                className: CONFIG.COLUMNS.ACTION.className
            },
        ],
        order: CONFIG.DATATABLE.DEFAULT_ORDER,
        dom: CONFIG.DATATABLE.DOM_LAYOUT,
        language: {
            processing: CONFIG.DATATABLE.PROCESSING_HTML
        },
        rowCallback: function(row, data, index) {
            // Apply background color and styling
            $(row).css('background-color', data.backgroundColor);
            $(row).addClass(`${data.levelClass} ${data.typeClass}`);

            // Add search highlighting
            if (data.isSearchMatch) {
                $(row).addClass('search-match');
            } else {
                $(row).removeClass('search-match');
            }

            // Add data attributes for styling hooks
            $(row).attr({
                'data-level': data.level,
                'data-tree': data.tree,
                'data-is-group': data.isGroup
            });

            return row;
        },
    };

    // Initialize DataTable
    const table = $('#datatable').DataTable(dataTableConfig);

    // ===== EVENT HANDLERS =====

    // Custom search functionality for DataTable
    $("#searchInput").on("keyup", function () {
        state.setSearching(true);
        table.search(this.value).draw();
    });

    // TreeView toggle functionality
    $('#datatable tbody').on('click', '.tree-toggle', function(e) {
        e.preventDefault();
        e.stopPropagation();
        const nodeId = parseInt($(this).data('id'));
        toggleTreeNode(nodeId);
    });

    // Prevent row selection when clicking on tree toggle
    $('#datatable tbody').on('click', 'tr', function(e) {
        if ($(e.target).closest('.tree-toggle').length > 0) {
            e.preventDefault();
            e.stopPropagation();
        }
    });

    // Add Data button click handler
    $('.add-btn, #addDataBtn').on('click', function(e) {
        e.preventDefault();
        ModalManager.resetForm('add');
        $(CONFIG.UI.MODAL_SELECTORS.MODAL + ', #modal-dialog').modal('show');
    });

    // Add Child Data button click handler
    $('#datatable').on('click', '.btn-add-child', function(e) {
        e.preventDefault();

        const parentId = $(this).data('id');
        const parentName = $(this).data('name');

        if (!parentId || !parentName) {
            NotificationHelper.showError('Data parent tidak ditemukan');
            return;
        }

        // Reset form for add mode
        ModalManager.resetForm('add');

        // Update modal title to indicate this is for adding child
        $(CONFIG.UI.MODAL_SELECTORS.TITLE).text(`Tambah Data Ruangan - Child of ${parentName}`);

        // Show modal first, then populate parent field after Select2 is initialized
        $(CONFIG.UI.MODAL_SELECTORS.MODAL).modal('show');

        // Set a flag to populate parent field after modal is shown
        $(CONFIG.UI.MODAL_SELECTORS.MODAL).data('parent-id', parentId);
        $(CONFIG.UI.MODAL_SELECTORS.MODAL).data('parent-name', parentName);
    });

    // Tree select change handler
    $(CONFIG.UI.FORM_FIELDS.TREE).on('change', function() {
        const selectedTree = $(this).val();
        if (selectedTree === 'BRANCH' || selectedTree === 'LEAF') {
            $('#parentGroup').show();
            const isRequired = selectedTree === 'LEAF';
            $(CONFIG.UI.FORM_FIELDS.PARENT_ID).prop('required', isRequired);

            const label = $(CONFIG.UI.FORM_FIELDS.PARENT_ID).closest('.mb-3').find('label[for="parent_id"]');
            if (isRequired) {
                label.html('Parent <span class="text-danger">*</span>');
            } else {
                label.html('Parent <span class="optional-text">(opsional)</span>');
            }
        } else {
            $('#parentGroup').hide();
            $(CONFIG.UI.FORM_FIELDS.PARENT_ID).prop('required', false);
        }
    });

    // Form submission handler
    $(CONFIG.UI.MODAL_SELECTORS.FORM).on('submit', function(e) {
        e.preventDefault();
        ModalManager.submitForm();
    });

    // Edit functionality
    $("#datatable").on("click", ".btn-edit", function () {
        const route = $(this).data("route");

        if (!route) {
            NotificationHelper.showError('Route tidak ditemukan');
            return;
        }

        AjaxManager.getRoomData(route)
            .done(function (response) {
                ModalManager.populateFormForEdit(response.data);
                $(CONFIG.UI.MODAL_SELECTORS.MODAL).modal('show');
            })
            .fail(function (xhr) {
                const errorMessage = xhr.responseJSON?.message || "Terjadi kesalahan saat mengambil data";
                NotificationHelper.showError(errorMessage);
            });
    });

    // Delete functionality with tree state preservation
    $("#datatable").on("click", ".btn-delete", function() {
        const route = $(this).data("route");

        NotificationHelper.showConfirmDelete((isConfirm) => {
            if (isConfirm) {
                const savedTreeState = state.captureTreeState();

                AjaxManager.deleteRoom(route)
                    .done(function(response) {
                        state.roomDataFromServer = [];

                        table.ajax.reload(function() {
                            state.restoreTreeState(savedTreeState);
                        }, false);

                        NotificationHelper.showSuccess(response.message);
                    })
                    .fail(function(xhr) {
                        const errorMessage = xhr.responseJSON?.message || "Terjadi kesalahan saat menghapus data";
                        NotificationHelper.showError(errorMessage);
                        state.restoreTreeState(savedTreeState);
                    });
            }
        });
    });

    // Modal event handlers
    $(CONFIG.UI.MODAL_SELECTORS.MODAL).on('shown.bs.modal', function () {
        ModalManager.initializeParentSelect2();

        // Check if this modal was opened from btn-add-child
        const parentId = $(this).data('parent-id');
        const parentName = $(this).data('parent-name');
        const findParent = state.roomDataFromServer.find(item => item.id === parentId);

        if (parentId && parentName && findParent) {
            // Create and select the parent option
            const parentOption = new Option(`${findParent.room_service_code} - ${findParent.room_service_name}`, parentId, true, true);
            $(CONFIG.UI.FORM_FIELDS.PARENT_ID).append(parentOption).trigger('change');

            // Clear the data attributes after use
            $(this).removeData('parent-id parent-name');
        }

        if (state.modalMode === 'add') {
            $(CONFIG.UI.FORM_FIELDS.ROOM_NAME).focus();
        }
    });

    $(CONFIG.UI.MODAL_SELECTORS.MODAL).on('hidden.bs.modal', function () {
        $(CONFIG.UI.FORM_FIELDS.PARENT_ID).select2('destroy');
        state.setModalMode('add');
    });

    // ===== TREE CONTROL EVENT HANDLERS =====

    // Expand All button click handler
    $('#expandAllBtn').on('click', function(e) {
        e.preventDefault();
        expandAllTreeNodes();
    });

    // Collapse All button click handler
    $('#collapseAllBtn').on('click', function(e) {
        e.preventDefault();
        collapseAllTreeNodes();
    });

    // ===== EXCEL IMPORT FUNCTIONALITY =====

    // Import Excel button click handler
    $('.import-btn').on('click', function(e) {
        e.preventDefault();
        // Reset form and messages
        $('#form-import-excel')[0].reset();
        $('.error-message').text('');
        resetImportMessageArea();
        $('#modal-import-excel').modal('show');
    });

    // Handle import submit
    $('#btn-import-submit').on('click', function(e) {
        e.preventDefault();

        const fileInput = $('#excel_file')[0];
        const file = fileInput.files[0];

        // Validate file
        if (!file) {
            $('#error_excel_file').text('Silakan pilih file Excel terlebih dahulu.');
            return;
        }

        // Enhanced file validation
        const validationResult = validateExcelFile(file);
        if (!validationResult.isValid) {
            $('#error_excel_file').text(validationResult.message);
            return;
        }

        // Clear previous errors
        $('.error-message').text('');

        // Show loading state
        const originalText = $('#btn-import-submit').html();
        $('#btn-import-submit').html('<i class="fas fa-spinner fa-spin me-1"></i>Mengimport...').prop('disabled', true);

        // Create FormData
        const formData = new FormData();
        formData.append('excel_file', file);
        formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

        // Capture current tree state before import
        const savedTreeState = state.captureTreeState();

        // Submit form via AJAX
        $.ajax({
            url: '/master-data/aspak/room/import',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                // Reset loading state
                $('#btn-import-submit').html(originalText).prop('disabled', false);

                if (response.success) {
                    // Reset form
                    $('#form-import-excel')[0].reset();
                    $('.error-message').text('');

                    // Clear server data cache to force reload
                    state.roomDataFromServer = [];

                    // Reload datatable with tree state preservation
                    table.ajax.reload(function() {
                        // Restore tree state after reload
                        state.restoreTreeState(savedTreeState);
                    }, false);

                    // Close modal
                    $('#modal-import-excel').modal('hide');

                    // Show success message
                    NotificationHelper.showSuccess(response.message || 'Data ASPAK Service Room berhasil diimport.');
                } else {
                    // Handle failed response with detailed errors
                    if (response.errors && Array.isArray(response.errors)) {
                        showDetailedImportErrors(response);
                    } else {
                        showImportErrorMessage(response.message || 'Terjadi kesalahan saat mengimport data.');
                    }
                }
            },
            error: function(xhr) {
                // Reset loading state
                $('#btn-import-submit').html(originalText).prop('disabled', false);

                let errorMessage = 'Terjadi kesalahan saat mengimport data.';
                let isFileValidationError = false;

                if (xhr.responseJSON) {
                    if (xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                        
                        // Check for specific OLE file error
                        if (errorMessage.includes('OLE file') || errorMessage.includes('not recognised')) {
                            errorMessage = 'File Excel yang dipilih tidak valid atau rusak. Silakan coba dengan file Excel yang berbeda atau download template baru.';
                            isFileValidationError = true;
                        }
                        // Check for other file-related errors
                        else if (errorMessage.includes('Invalid file') || errorMessage.includes('file format')) {
                            errorMessage = 'Format file tidak didukung atau file rusak. Pastikan menggunakan file Excel (.xlsx atau .xls) yang valid.';
                            isFileValidationError = true;
                        }
                        // Check for header validation errors
                        else if (errorMessage.includes('header') || errorMessage.includes('kolom')) {
                            errorMessage = 'Format header file Excel tidak sesuai. Silakan download template dan pastikan menggunakan format yang benar.';
                            isFileValidationError = true;
                        }
                    }

                    // Handle validation errors for file upload
                    if (xhr.responseJSON.errors && xhr.responseJSON.errors.excel_file) {
                        $('#error_excel_file').text(xhr.responseJSON.errors.excel_file[0]);
                        return;
                    }

                    // Handle detailed import errors
                    if (xhr.responseJSON.errors && Array.isArray(xhr.responseJSON.errors)) {
                        showDetailedImportErrors(xhr.responseJSON);
                        return;
                    }
                }

                // Show file validation error in the file input error area
                if (isFileValidationError) {
                    $('#error_excel_file').text(errorMessage);
                } else {
                    showImportErrorMessage(errorMessage);
                }
            }
        });
    });

    // Download template button click handler
    $('#btn-download-template').on('click', function(e) {
        e.preventDefault();
        
        // Show loading state
        const originalText = $(this).html();
        $(this).html('<i class="fas fa-spinner fa-spin me-1"></i>Downloading...').prop('disabled', true);
        
        // Create a temporary link to download the template
        const link = document.createElement('a');
        link.href = '/master-data/aspak/room/download-template';
        link.download = 'template_aspak_service_room.xlsx';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // Reset button state after a short delay
        setTimeout(() => {
            $(this).html(originalText).prop('disabled', false);
        }, 1000);
    });

    // Reset form when modal is hidden
    $('#modal-import-excel').on('hidden.bs.modal', function() {
        $('#form-import-excel')[0].reset();
        $('.error-message').text('');
        $('#btn-import-submit').html('<i class="fas fa-upload me-1"></i>Import').prop('disabled', false);
        // Reset message area and show form again
        resetImportMessageArea();
        $('#form-import-excel').show();
    });

    // ===== IMPORT HELPER FUNCTIONS =====

    /**
     * Enhanced Excel file validation with MIME type and file signature checking
     */
    function validateExcelFile(file) {
        // Check if file exists
        if (!file) {
            return {
                isValid: false,
                message: 'Silakan pilih file Excel terlebih dahulu.'
            };
        }

        // Validate file extension
        const allowedExtensions = ['.xlsx', '.xls'];
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        if (!allowedExtensions.includes(fileExtension)) {
            return {
                isValid: false,
                message: 'Format file tidak didukung. Gunakan file Excel (.xlsx atau .xls) yang valid.'
            };
        }

        // Validate MIME type
        const allowedMimeTypes = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
            'application/vnd.ms-excel', // .xls
            'application/excel',
            'application/x-excel',
            'application/x-msexcel'
        ];
        
        if (file.type && !allowedMimeTypes.includes(file.type)) {
            return {
                isValid: false,
                message: 'File yang dipilih bukan file Excel yang valid. Pastikan file memiliki format .xlsx atau .xls yang benar.'
            };
        }

        // Validate file size (max 5MB)
        const maxSize = 5 * 1024 * 1024; // 5MB in bytes
        if (file.size > maxSize) {
            return {
                isValid: false,
                message: 'Ukuran file terlalu besar. Maksimal 5MB.'
            };
        }

        // Check minimum file size (empty files)
        if (file.size < 100) {
            return {
                isValid: false,
                message: 'File terlalu kecil atau kosong. Pastikan file Excel berisi data yang valid.'
            };
        }

        return {
            isValid: true,
            message: 'File valid'
        };
    }

    /**
     * Reset import message area to show form
     */
    function resetImportMessageArea() {
        $('#import-message-area').addClass('d-none');
        $('#import-success-message').addClass('d-none');
        $('#import-error-message').addClass('d-none');
        $('#form-import-excel').show();
    }

    /**
     * Show import success message
     */
    function showImportSuccessMessage(message) {
        $('#success-text').text(message);
        $('#import-success-message').removeClass('d-none');
        $('#import-error-message').addClass('d-none');
        $('#import-message-area').removeClass('d-none');
    }

    /**
     * Show import error message
     */
    function showImportErrorMessage(message, details = null) {
        $('#error-text').text(message);
        if (details) {
            $('#error-details').html(details);
        }
        $('#import-error-message').removeClass('d-none');
        $('#import-success-message').addClass('d-none');
        $('#import-message-area').removeClass('d-none');
    }

    /**
     * Show detailed import errors with hierarchical data context
     */
    function showDetailedImportErrors(response) {
        // Group errors by row number
        const errorsByRow = {};

        response.errors.forEach(function(error) {
            const rowNumber = error.row;
            if (!errorsByRow[rowNumber]) {
                errorsByRow[rowNumber] = {
                    values: error.values || {},
                    errors: []
                };
            }
            errorsByRow[rowNumber].errors.push({
                attribute: error.attribute,
                messages: error.errors
            });
        });

        let errorHtml = `
            <div class="text-start">
                <p><strong>Total Error:</strong> ${response.error_count || response.errors.length}</p>
                <div style="max-height: 400px; overflow-y: auto;">
        `;

        // Sort row numbers for consistent display
        const sortedRows = Object.keys(errorsByRow).sort((a, b) => parseInt(a) - parseInt(b));

        sortedRows.forEach(function(rowNumber) {
            const rowData = errorsByRow[rowNumber];
            const values = rowData.values;

            errorHtml += `
                <div class="card mb-3 border-danger">
                    <div class="card-header bg-danger text-white">
                        <h6 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Baris ${rowNumber}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-danger mb-2">Error yang ditemukan:</h6>
                                <ul class="list-unstyled mb-0">
            `;

            rowData.errors.forEach(function(errorItem) {
                errorHtml += `
                                    <li class="mb-1">
                                        <strong class="text-primary">${errorItem.attribute}:</strong>
                                        <span class="text-danger">${errorItem.messages.join(', ')}</span>
                                    </li>
                `;
            });

            errorHtml += `
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-info mb-2">Data pada baris ini:</h6>
                                <div class="small">
                                    <div class="mb-1"><strong>Kode:</strong> ${values.kode || '-'}</div>
                                    <div class="mb-1"><strong>Nama:</strong> ${values.nama || '-'}</div>
                                    <div class="mb-1"><strong>Parent:</strong> ${values.parent || '-'}</div>
                                    <div class="mb-1"><strong>Tree:</strong> ${values.tree || '-'}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        errorHtml += `
                </div>
            </div>
        `;

        // Show error message with details in modal
        showImportErrorMessage('Import Gagal! Terdapat error pada data yang diimport.', errorHtml);
    }
});
