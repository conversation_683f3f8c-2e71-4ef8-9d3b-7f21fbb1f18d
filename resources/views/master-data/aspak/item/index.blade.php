@extends('layouts.app')

@section('title', 'ASPAK Item')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">ASPAK Item</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#addEditModal">
                            <i class="fas fa-plus"></i> Tambah Data
                        </button>
                        <button type="button" class="btn btn-success btn-sm" data-toggle="modal" data-target="#importModal">
                            <i class="fas fa-file-excel"></i> Import Excel
                        </button>
                        <a href="{{ route('master-data.aspak-item.download-template') }}" class="btn btn-info btn-sm">
                            <i class="fas fa-download"></i> Download Template
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="search">Pencarian:</label>
                                <input type="text" class="form-control" id="search" placeholder="Cari kode atau nama item...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="tree-filter">Filter Tree:</label>
                                <select class="form-control" id="tree-filter">
                                    <option value="">Semua</option>
                                    <option value="BRANCH">Group</option>
                                    <option value="LEAF">Item</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>&nbsp;</label><br>
                                <button type="button" class="btn btn-secondary btn-sm" id="expand-all">
                                    <i class="fas fa-expand-arrows-alt"></i> Expand All
                                </button>
                                <button type="button" class="btn btn-secondary btn-sm" id="collapse-all">
                                    <i class="fas fa-compress-arrows-alt"></i> Collapse All
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="table-responsive">
                        <table id="aspak-item-table" class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th width="5%">No</th>
                                    <th width="15%">Kode Item</th>
                                    <th width="35%">Nama Item</th>
                                    <th width="25%">Sinonim</th>
                                    <th width="10%">Tipe</th>
                                    <th width="10%">Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data will be loaded via AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Data Modal -->
<div class="modal fade" id="addEditModal" tabindex="-1" role="dialog" aria-labelledby="addEditModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addEditModalLabel">Tambah Data</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="aspak-item-form">
                @csrf
                <input type="hidden" id="item-id" name="id">
                <input type="hidden" id="form-method" name="_method" value="POST">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="item_code">Kode Item <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="item_code" name="item_code" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="tree">Tipe <span class="text-danger">*</span></label>
                                <select class="form-control" id="tree" name="tree" required>
                                    <option value="">Pilih Tipe</option>
                                    <option value="BRANCH">Group</option>
                                    <option value="LEAF">Item</option>
                                </select>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="item_name">Nama Item <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="item_name" name="item_name" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="item_synonym">Sinonim</label>
                                <input type="text" class="form-control" id="item_synonym" name="item_synonym">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="parent_id">Parent Group</label>
                                <select class="form-control select2" id="parent_id" name="parent_id" style="width: 100%;">
                                    <option value="">Pilih Parent Group (Opsional)</option>
                                </select>
                                <div class="invalid-feedback"></div>
                                <small class="form-text text-muted">Kosongkan jika ini adalah root item</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary" id="save-btn">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Import Excel Modal -->
<div class="modal fade" id="importModal" tabindex="-1" role="dialog" aria-labelledby="importModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importModalLabel">Import Data dari Excel</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="import-form" enctype="multipart/form-data">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="excel_file">File Excel <span class="text-danger">*</span></label>
                        <input type="file" class="form-control-file" id="excel_file" name="excel_file" accept=".xlsx,.xls" required>
                        <small class="form-text text-muted">Format yang didukung: .xlsx, .xls (Maksimal 2MB)</small>
                        <div class="invalid-feedback"></div>
                    </div>
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> Petunjuk Import:</h6>
                        <ul class="mb-0">
                            <li>Download template Excel terlebih dahulu</li>
                            <li>Isi data sesuai format yang tersedia</li>
                            <li>Pastikan tidak ada data yang kosong pada kolom wajib</li>
                            <li>Untuk Parent Group, gunakan ID yang valid atau kosongkan untuk root item</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-success" id="import-btn">
                        <i class="fas fa-upload"></i> Import
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('styles')
<link rel="stylesheet" href="{{ asset('plugins/datatables-bs4/css/dataTables.bootstrap4.min.css') }}">
<link rel="stylesheet" href="{{ asset('plugins/datatables-responsive/css/responsive.bootstrap4.min.css') }}">
<link rel="stylesheet" href="{{ asset('plugins/select2/css/select2.min.css') }}">
<link rel="stylesheet" href="{{ asset('plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css') }}">
<style>
    /* TreeView Styles */
    .tree-level-0 { background-color: #f8f9fa; font-weight: bold; }
    .tree-level-1 { background-color: #e9ecef; padding-left: 20px; }
    .tree-level-2 { background-color: #dee2e6; padding-left: 40px; }
    .tree-level-3 { background-color: #ced4da; padding-left: 60px; }
    .tree-level-4 { background-color: #adb5bd; padding-left: 80px; }
    
    .tree-group-row {
        cursor: pointer;
        transition: background-color 0.2s;
    }
    
    .tree-group-row:hover {
        background-color: #007bff !important;
        color: white;
    }
    
    .tree-item-row {
        background-color: #ffffff;
    }
    
    .tree-item-row:hover {
        background-color: #f1f3f4;
    }
    
    .tree-toggle {
        cursor: pointer;
        margin-right: 5px;
        font-size: 12px;
    }
    
    .tree-hidden {
        display: none;
    }
    
    .select2-container--bootstrap4 .select2-selection {
        height: calc(2.25rem + 2px) !important;
    }
</style>
@endpush

@push('scripts')
<script src="{{ asset('plugins/datatables/jquery.dataTables.min.js') }}"></script>
<script src="{{ asset('plugins/datatables-bs4/js/dataTables.bootstrap4.min.js') }}"></script>
<script src="{{ asset('plugins/datatables-responsive/js/dataTables.responsive.min.js') }}"></script>
<script src="{{ asset('plugins/datatables-responsive/js/responsive.bootstrap4.min.js') }}"></script>
<script src="{{ asset('plugins/select2/js/select2.full.min.js') }}"></script>
<script src="{{ asset('plugins/sweetalert2/sweetalert2.min.js') }}"></script>
<script src="{{ asset('js/app/master/aspak-item.js') }}"></script>
@endpush