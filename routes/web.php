<?php

use App\Http\Controllers\AssetController;
use App\Http\Controllers\AssetMaintenance\HomeController as AssetMaintenanceHomeController;
use App\Http\Controllers\AssetMaintenance\MaintenanceIncidentalActivityController;
use App\Http\Controllers\AssetMaintenance\MaintenanceIncidentalDecisionController;
use App\Http\Controllers\AssetMaintenance\MaintenanceIncidentalRequestController;
use App\Http\Controllers\AssetMaintenance\MaintenanceMedicalActivityController;
use App\Http\Controllers\AssetMaintenance\MaintenanceMedicalScheduleController;
use App\Http\Controllers\AssetMaintenance\MaintenanceNonMedicalActivityController;
use App\Http\Controllers\AssetMaintenance\MaintenanceNonMedicalScheduleController;
use App\Http\Controllers\AssetMaintenance\MaintenanceReportController;
use App\Http\Controllers\AssetManagement\AssetCategoryController;
use App\Http\Controllers\AssetManagement\AssetDeletionController;
use App\Http\Controllers\AssetManagement\AssetDocumentController;
use App\Http\Controllers\AssetManagement\AssetHistoryController;
use App\Http\Controllers\AssetManagement\AssetHospitalController;
use App\Http\Controllers\AssetManagement\AssetMutationController;
use App\Http\Controllers\AssetManagement\AssetPositionController;
use App\Http\Controllers\AssetManagement\AssetReportController;
use App\Http\Controllers\AssetManagement\DamagedAssetController;
use App\Http\Controllers\AssetManagement\HelperBookController;
use App\Http\Controllers\AssetManagement\HomeController as AssetManagementHomeController;
use App\Http\Controllers\AssetManagement\PositionChangeController;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Depreciation\DepreciationReportController;
use App\Http\Controllers\Depreciation\DepreciationSettingController;
use App\Http\Controllers\Depreciation\HomeController as DepreciationHomeController;
use App\Http\Controllers\Home\HomeController;
use App\Http\Controllers\Logistic\AdjustmentAssetController;
use App\Http\Controllers\Logistic\AssetFulfillmentController;
use App\Http\Controllers\Logistic\AssetLogisticController;
use App\Http\Controllers\Logistic\AssetRequestController;
use App\Http\Controllers\Logistic\HomeController as LogisticHomeController;
use App\Http\Controllers\Logistic\IncomingAssetController;
use App\Http\Controllers\Logistic\LogisticReportController;
use App\Http\Controllers\Logistic\OutgoingItemController;
use App\Http\Controllers\Logistic\StockRecapController;
use App\Http\Controllers\MasterData\AspakRoomController;
use App\Http\Controllers\MasterData\AspakItemController;
use App\Http\Controllers\MasterData\CategoryController;
use App\Http\Controllers\MasterData\ConfigStockRecapController;
use App\Http\Controllers\MasterData\DistributorController;
use App\Http\Controllers\MasterData\DivisionController;
use App\Http\Controllers\MasterData\EmployeeController;
use App\Http\Controllers\MasterData\HomeController as MasterDataHomeController;
use App\Http\Controllers\MasterData\ItemController;
use App\Http\Controllers\MasterData\MaintenanceCategoryController;
use App\Http\Controllers\MasterData\OfficerController;
use App\Http\Controllers\MasterData\ProgramController;
use App\Http\Controllers\MasterData\RoleController;
use App\Http\Controllers\MasterData\RoomCategoryController;
use App\Http\Controllers\MasterData\RoomController;
use App\Http\Controllers\MasterData\RoomProgramController;
use App\Http\Controllers\MasterData\RoomSubController;
use App\Http\Controllers\MasterData\UomController;
use App\Http\Controllers\Planning\ApprovalAssetMaintenanceController;
use App\Http\Controllers\Planning\ApprovalConsumableProcurementController;
use App\Http\Controllers\Planning\AssetInfrastructureController;
use App\Http\Controllers\Planning\AssetMaintenanceController;
use App\Http\Controllers\Planning\ConsumableProcurementController;
use App\Http\Controllers\Planning\HomeController as PlanningHomeController;
use App\Http\Controllers\Planning\PlanningReportController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded automatically by Laravel and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return view('pages.auth.login');
})->middleware("guest");
Route::get("/login", [LoginController::class, "index"])->name("login.index")->middleware("guest");
Route::post("/login", [LoginController::class, "login"])->name("login")->middleware("guest");
Route::post("/logout", [LoginController::class, "logout"])->name("logout")->middleware("multi-auth");

// New Route
Route::middleware(["multi-auth"])->group(function () {
    // Home
    Route::get("home", [HomeController::class, "index"])->name("home.index");
    // Master Data
    Route::prefix("master-data")->name("master-data.")->group(function () {
        Route::get("/dashboard", [MasterDataHomeController::class, "index"])->name("home.index");
        // Role
        Route::get("data-role", [RoleController::class, "index"])->name("role.index");
        Route::get("data-role-list", [RoleController::class, "list"])->name("role.list");
        Route::get("data-role/{role:id}", [RoleController::class, "show"])->name("role.show");
        Route::post("data-role/store", [RoleController::class, "store"])->name("role.store");
        Route::put("data-role/{role:id}", [RoleController::class, "update"])->name("role.update");

        // Employee
        Route::get("data-karyawan", [EmployeeController::class, "index"])->name("employee.index");
        Route::get("data-karyawan-list", [EmployeeController::class, "list"])->name("employee.list");
        Route::get("data-karyawan/{employee:id}", [EmployeeController::class, "show"])->name("employee.show");
        Route::get("data-karyawan/find/type", [EmployeeController::class, "findByType"])->name("employee.findType");
        Route::post("data-karyawan/store", [EmployeeController::class, "store"])->name("employee.store");
        Route::put("data-karyawan/{employee:id}", [EmployeeController::class, "update"])->name("employee.update");
        Route::delete("data-karyawan/{employee:id}", [EmployeeController::class, "destroy"])->name("employee.destroy");
        Route::post("data-karyawan/import", [EmployeeController::class, "import"])->name("employee.import");
        Route::get("data-karyawan-download-template", [EmployeeController::class, "downloadTemplate"])->name("employee.download-template");

        // Officer
        Route::get("data-petugas", [OfficerController::class, "index"])->name("officer.index");
        Route::get("data-petugas-list", [OfficerController::class, "list"])->name("officer.list");
        Route::get("data-petugas/{officer:id}", [OfficerController::class, "show"])->name("officer.show");
        Route::get("data-petugas/find/type", [OfficerController::class, "findByType"])->name("officer.findType");
        Route::post("data-petugas/store", [OfficerController::class, "store"])->name("officer.store");
        Route::put("data-petugas/{officer:id}", [OfficerController::class, "update"])->name("officer.update");
        Route::delete("data-petugas/{officer:id}", [OfficerController::class, "destroy"])->name("officer.destroy");

        // Program
        Route::get("data-program", [ProgramController::class, "index"])->name("program.index");
        Route::get("data-program-list", [ProgramController::class, "list"])->name("program.list");
        Route::get("data-program/{program:id}", [ProgramController::class, "show"])->name("program.show");
        Route::get("data-program/find/type", [ProgramController::class, "findByType"])->name("program.findType");
        Route::post("data-program/store", [ProgramController::class, "store"])->name("program.store");
        Route::put("data-program/{program:id}", [ProgramController::class, "update"])->name("program.update");
        Route::delete("data-program/{program:id}", [ProgramController::class, "destroy"])->name("program.destroy");

        // Category
        Route::get("data-kategori", [CategoryController::class, "index"])->name("category.index");
        Route::get("data-kategori-list", [CategoryController::class, "list"])->name("category.list");
        Route::get("data-kategori/{category:id}", [CategoryController::class, "show"])->name("category.show");
        Route::post("data-kategori/store", [CategoryController::class, "store"])->name("category.store");
        Route::put("data-kategori/{category:id}", [CategoryController::class, "update"])->name("category.update");
        Route::delete("data-kategori/{category:id}", [CategoryController::class, "destroy"])->name("category.destroy");
        Route::post("data-kategori/import", [CategoryController::class, "import"])->name("category.import");
        Route::get("data-kategori-download-template", [CategoryController::class, "downloadTemplate"])->name("category.download-template");

        // Room Category
        Route::get("data-kategori-ruangan", [RoomCategoryController::class, "index"])->name("room-category.index");
        Route::get("data-kategori-ruangan-list", [RoomCategoryController::class, "list"])->name("room-category.list");
        Route::get("data-kategori-ruangan/{roomCategory:id}", [RoomCategoryController::class, "show"])->name("room-category.show");
        Route::post("data-kategori-ruangan/store", [RoomCategoryController::class, "store"])->name("room-category.store");
        Route::put("data-kategori-ruangan/{roomCategory:id}", [RoomCategoryController::class, "update"])->name("room-category.update");
        Route::delete("data-kategori-ruangan/{roomCategory:id}", [RoomCategoryController::class, "destroy"])->name("room-category.destroy");

        // Maintenance Category
        Route::get("data-kategori-pemeliharaan", [MaintenanceCategoryController::class, "index"])->name("maintenance-category.index");
        Route::get("data-kategori-pemeliharaan-list", [MaintenanceCategoryController::class, "list"])->name("maintenance-category.list");
        Route::get("data-kategori-pemeliharaan/{id}", [MaintenanceCategoryController::class, "show"])->name("maintenance-category.show");
        Route::post("data-kategori-pemeliharaan/store", [MaintenanceCategoryController::class, "store"])->name("maintenance-category.store");
        Route::put("data-kategori-pemeliharaan/{id}", [MaintenanceCategoryController::class, "update"])->name("maintenance-category.update");
        Route::delete("data-kategori-pemeliharaan/{id}", [MaintenanceCategoryController::class, "destroy"])->name("maintenance-category.destroy");

        // Room
        Route::get("data-ruangan", [RoomController::class, "index"])->name("room.index");
        Route::get("data-ruangan-list", [RoomController::class, "list"])->name("room.list");
        Route::get("data-ruangan/{room:id}", [RoomController::class, "show"])->name("room.show");
        Route::post("data-ruangan/store", [RoomController::class, "store"])->name("room.store");
        Route::put("data-ruangan/{room:id}", [RoomController::class, "update"])->name("room.update");
        Route::delete("data-ruangan/{room:id}", [RoomController::class, "destroy"])->name("room.destroy");
        Route::get("data-ruangan-download-template", [RoomController::class, "downloadTemplate"])->name("room.download-template");
        Route::post("data-ruangan/import", [RoomController::class, "import"])->name("room.import");

        // Room Sub
        Route::get("data-ruangan/{room}/sub-ruangan", [RoomSubController::class, "index"])->name("room-sub.index");
        Route::get("data-ruangan/{room}/sub-ruangan-list", [RoomSubController::class, "list"])->name("room-sub.list");
        Route::post("data-ruangan/{room}/sub-ruangan", [RoomSubController::class, "store"])->name("room-sub.store");
        Route::get("data-ruangan/{room:id}/sub-ruangan/{roomSub:id}", [RoomSubController::class, "show"])->name("room-sub.show");
        Route::put("data-ruangan/{room:id}/sub-ruangan/{roomSub:id}", [RoomSubController::class, "update"])->name("room-sub.update");
        Route::delete("data-ruangan/{room:id}/sub-ruangan/{roomSub:id}", [RoomSubController::class, "destroy"])->name("room-sub.destroy");

        // Item
        Route::get("data-barang", [ItemController::class, "index"])->name("item.index");
        Route::get("data-barang-list", [ItemController::class, "list"])->name("item.list");
        Route::get("data-barang/{item:id}", [ItemController::class, "show"])->name("item.show");
        Route::post("data-barang/store", [ItemController::class, "store"])->name("item.store");
        Route::put("data-barang/{item:id}", [ItemController::class, "update"])->name("item.update");
        Route::delete("data-barang/{item:id}", [ItemController::class, "destroy"])->name("item.destroy");
        Route::post("data-barang/import", [ItemController::class, "import"])->name("item.import");
        Route::get("data-barang-download-template", [ItemController::class, "downloadTemplate"])->name("item.download-template");

        // Distributor
        Route::get("data-distributor", [DistributorController::class, "index"])->name("distributor.index");
        Route::get("data-distributor-list", [DistributorController::class, "list"])->name("distributor.list");
        Route::get("data-distributor/{distributor:id}", [DistributorController::class, "show"])->name("distributor.show");
        Route::post("data-distributor/store", [DistributorController::class, "store"])->name("distributor.store");
        Route::put("data-distributor/{distributor:id}", [DistributorController::class, "update"])->name("distributor.update");
        Route::delete("data-distributor/{distributor:id}", [DistributorController::class, "destroy"])->name("distributor.destroy");
        Route::post("data-distributor/import", [DistributorController::class, "import"])->name("distributor.import");
        Route::get("data-distributor-download-template", [DistributorController::class, "downloadTemplate"])->name("distributor.download-template");

        // Config Rekapitulasi Stok
        Route::get("data-rekapitulasi-stok", [ConfigStockRecapController::class, "index"])->name("config-stock-recap.index");
        Route::get("data-rekapitulasi-stok-list", [ConfigStockRecapController::class, "list"])->name("config-stock-recap.list");
        Route::get("data-rekapitulasi-stok/{configStockRecap:id}", [ConfigStockRecapController::class, "show"])->name("config-stock-recap.show");
        Route::post("data-rekapitulasi-stok/store", [ConfigStockRecapController::class, "store"])->name("config-stock-recap.store");
        Route::put("data-rekapitulasi-stok/{configStockRecap:id}", [ConfigStockRecapController::class, "update"])->name("config-stock-recap.update");
        Route::delete("data-rekapitulasi-stok/{configStockRecap:id}", [ConfigStockRecapController::class, "destroy"])->name("config-stock-recap.destroy");

        // ASPAK Room
        Route::get("aspak/room", [AspakRoomController::class, "index"])->name("aspak-room.index");
        Route::get("aspak/room/list", [AspakRoomController::class, "list"])->name("aspak-room.list");
        Route::post("aspak/room", [AspakRoomController::class, "store"])->name("aspak-room.store");
        Route::get("aspak/room/parent-groups", [AspakRoomController::class, "parentGroups"])->name("aspak-room.parent-groups");
        Route::get("aspak/room/{aspakServiceRoom:id}/edit", [AspakRoomController::class, "edit"])->name("aspak-room.edit");
        Route::put("aspak/room/{aspakServiceRoom:id}", [AspakRoomController::class, "update"])->name("aspak-room.update");
        Route::delete("aspak/room/{aspakServiceRoom:id}", [AspakRoomController::class, "destroy"])->name("aspak-room.destroy");
        Route::post('aspak/room/import', [AspakRoomController::class, 'import'])->name('aspak-room.import');
        Route::get('aspak/room/download-template', [AspakRoomController::class, 'downloadTemplate'])->name('aspak-room.download-template');

        // ASPAK Item
        Route::get("aspak/item", [AspakItemController::class, "index"])->name("aspak-item.index");
        Route::get("aspak/item/list", [AspakItemController::class, "list"])->name("aspak-item.list");
        Route::post("aspak/item", [AspakItemController::class, "store"])->name("aspak-item.store");
        Route::get("aspak/item/parent-groups", [AspakItemController::class, "parentGroups"])->name("aspak-item.parent-groups");
        Route::get("aspak/item/{aspakItem:id}/edit", [AspakItemController::class, "edit"])->name("aspak-item.edit");
        Route::put("aspak/item/{aspakItem:id}", [AspakItemController::class, "update"])->name("aspak-item.update");
        Route::delete("aspak/item/{aspakItem:id}", [AspakItemController::class, "destroy"])->name("aspak-item.destroy");
        Route::post('aspak/item/import', [AspakItemController::class, 'import'])->name('aspak-item.import');
        Route::get('aspak/item/download-template', [AspakItemController::class, 'downloadTemplate'])->name('aspak-item.download-template');

        // UOM
        Route::get("data-uom", [UomController::class, "index"])->name("uom.index");
        Route::get("data-uom-list", [UomController::class, "list"])->name("uom.list");
        Route::get("data-uom/{uom:id}", [UomController::class, "show"])->name("uom.show");
        Route::post("data-uom/store", [UomController::class, "store"])->name("uom.store");
        Route::put("data-uom/{uom:id}", [UomController::class, "update"])->name("uom.update");
        Route::delete("data-uom/{uom:id}", [UomController::class, "destroy"])->name("uom.destroy");
        Route::get("data-uom/dropdown/list", [UomController::class, "dropdown"])->name("uom.dropdown");

        // Room Program
        Route::get("data-ruangan/{room}/program", [RoomProgramController::class, "index"])->name("room-program.index");
        Route::get("data-ruangan/{room}/program/list", [RoomProgramController::class, "list"])->name("room-program.list");
        Route::post("data-ruangan/{room}/program", [RoomProgramController::class, "store"])->name("room-program.store");
        Route::delete("data-ruangan/{room}/program/{program}", [RoomProgramController::class, "destroy"])->name("room-program.destroy");

        // Division
        Route::get("data-bidang", [DivisionController::class, "index"])->name("division.index");
        Route::get("data-bidang-list", [DivisionController::class, "list"])->name("division.list");
        Route::get("data-bidang/{division:id}", [DivisionController::class, "show"])->name("division.show");
        Route::post("data-bidang", [DivisionController::class, "store"])->name("division.store");
        Route::put("data-bidang/{division:id}", [DivisionController::class, "update"])->name("division.update");
        Route::delete("data-bidang/{division:id}", [DivisionController::class, "destroy"])->name("division.destroy");
    });

    // Asset Management
    Route::prefix("manajemen-aset")->name("asset-management.")->group(function () {
        Route::get("/dashboard", [AssetManagementHomeController::class, "index"])->name("home.index");
        // Asset Hospital
        Route::get("data-aset", [AssetHospitalController::class, "index"])->name("asset-hospital.index");
        Route::get("data-aset/tambah", [AssetHospitalController::class, "create"])->name("asset-hospital.create");
        Route::get("data-aset/{asset:id}/edit", [AssetHospitalController::class, "edit"])->name("asset-hospital.edit");
        Route::get("data-aset/register-code/generate", [AssetHospitalController::class, "generate_register_code"])->name("asset-hospital.registercode.generate");
        Route::get("data-aset-list", [AssetHospitalController::class, "list"])->name("asset-hospital.list");
        Route::get("data-aset/alokasi/aset", [AssetHospitalController::class, "allocation"])->name("asset-hospital.allocation");
        Route::get("data-aset/template/download", [AssetHospitalController::class, "download_template"])->name("asset-hospital.template.download");
        Route::get("data-aset/print-label", [AssetHospitalController::class, "print_label"])->name("asset-hospital.print_label");
        Route::get("data-aset/{asset:id}/show", [AssetHospitalController::class, "show"])->name("asset-hospital.asset.show");
        Route::put("data-aset/{assetEntry:id}/update", [AssetHospitalController::class, "update"])->name("asset-hospital.update");
        Route::post("data-aset/store", [AssetHospitalController::class, "store"])->name("asset-hospital.store");
        Route::post("data-aset/alocation/store", [AssetHospitalController::class, "store_allocation"])->name("asset-hospital.allocation.store");
        Route::post("data-aset/import", [AssetHospitalController::class, "import"])->name("asset-hospital.import");
        Route::post("data-aset/upload-photo", [AssetHospitalController::class, "uploadPhoto"])->name("asset-hospital.upload-photo");

        // Asset Document
        Route::get("dokumen-aset", [AssetDocumentController::class, "index"])->name("asset-document.index");
        Route::get("dokumen-aset-list", [AssetDocumentController::class, "list"])->name("asset-document.list");
        Route::get("dokumen-aset/request-document/{documentCode}", [AssetDocumentController::class, "request_document_detail"])->name("asset-document.request_document_detail");
        Route::get("dokumen-aset/media/{asset:id}", [AssetDocumentController::class, "media"])->name("asset-document.media");
        Route::get("dokumen-aset/list-document/{asset:id}", [AssetDocumentController::class, "list_document"])->name("asset-document.list_document");
        Route::post("dokumen-aset/request-document", [AssetDocumentController::class, "request_document"])->name("asset-document.request_document");
        Route::post("dokumen-aset/upload", [AssetDocumentController::class, "upload"])->name("asset-document.upload");

        // Asset Mutation
        Route::get("bast-mutasi", [AssetMutationController::class, "index"])->name("asset-mutation.index");

        // Asset Category
        Route::get("kategori-aset", [AssetCategoryController::class, "index"])->name("asset-category.index");
        Route::get("kategori-aset-list", [AssetCategoryController::class, "list"])->name("asset-category.list");
        Route::get("kategori-aset/{category:id}", [AssetCategoryController::class, "show"])->name("asset-category.show");
        Route::get("kategori-aset/{category:id}/aset-list", [AssetCategoryController::class, "asset_list"])->name("asset-category.asset_list");
        Route::get('kategori-aset/{category}/export-excel', [AssetCategoryController::class, 'exportExcel'])->name('export-excel');

        // Asset Position
        Route::get("posisi-aset", [AssetPositionController::class, "index"])->name("asset-position.index");
        Route::get("posisi-aset-list", [AssetPositionController::class, "list"])->name("asset-position.list");
        Route::get("posisi-aset/{room:id}", [AssetPositionController::class, "show"])->name("asset-position.show");
        Route::get("posisi-aset/{room:id}/aset-list", [AssetPositionController::class, "asset_list"])->name("asset-position.asset_list");
        Route::get('posisi-aset/{room}/export-excel', [AssetPositionController::class, 'exportExcel'])->name('asset-position.export-excel');

        // Position Change
        Route::get("perubahan-posisi", [PositionChangeController::class, "index"])->name("position-change.index");
        Route::get("perubahan-posisi-list", [PositionChangeController::class, "list"])->name("position-change.list");

        Route::get("buku-bantu", [HelperBookController::class, "index"])->name("helper-book.index");
        Route::get("buku-bantu-list", [HelperBookController::class, "list"])->name("helper-book.list");
        Route::get("buku-bantu/dokumen/{document:id}", [HelperBookController::class, "dokumen"])->name("helper-book.dokumen");

        // Asset Deletion
        Route::get("penghapusan-aset", [AssetDeletionController::class, "index"])->name("asset-deletion.index");
        Route::get("penghapusan-aset-list", [AssetDeletionController::class, "list"])->name("asset-deletion.list");
        Route::get("penghapusan-aset/delete", [AssetDeletionController::class, "delete"])->name("asset-deletion.delete");
        Route::post("penghapusan-aset/store", [AssetDeletionController::class, "store"])->name("asset-deletion.store");

        // Damaged Asset
        Route::get("aset-rusak", [DamagedAssetController::class, "index"])->name("damaged-asset.index");
        Route::get("aset-rusak-list", [DamagedAssetController::class, "list"])->name("damaged-asset.list");
        Route::get('aset-rusak/{asset:id}/dokumen', [DamagedAssetController::class, 'document'])->name('damaged-asset.document');

        // Asset History
        Route::get("history-aset", [AssetHistoryController::class, "index"])->name("asset-history.index");
        Route::get("history-aset-list", [AssetHistoryController::class, "list"])->name("asset-history.list");
        Route::get("history-aset/{asset:id}", [AssetHistoryController::class, "history"])->name("asset-history.history");

        // Asset Report
        Route::get("laporan-aset", [AssetReportController::class, "index"])->name("asset-report.index");
        Route::get("laporan-penerimaan-inventaris", [AssetReportController::class, "penerimaan_inventaris"])->name("asset-report.penerimaan_inventaris");
        Route::get("laporan-aspak", [AssetReportController::class, "aspak"])->name("asset-report.aspak");
        Route::get("laporan-daftar-barang", [AssetReportController::class, "daftar_barang"])->name("asset-report.daftar_barang");
        Route::get("laporan-kir", [AssetReportController::class, "kir"])->name("asset-report.kir");
        Route::get("laporan-kir-barang", [AssetReportController::class, "kir_barang"])->name("asset-report.kir_barang");

        Route::get("export-penerimaan-inventaris", [AssetReportController::class, "export_penerimaan_inventaris"])->name("export-report.penerimaan_inventaris");
        Route::get("export-aspak", [AssetReportController::class, "export_aspak"])->name("export-report.aspak");
        Route::get("export-daftar-barang", [AssetReportController::class, "export_daftar_barang"])->name("export-report.daftar_barang");
        Route::get("export-kir", [AssetReportController::class, "export_kir"])->name("export-report.kir");
        Route::get("export-kir-barang", [AssetReportController::class, "export_kir_barang"])->name("export-report.kir-barang");
    });

    // Pemeliharaan
    Route::prefix("pemeliharaan-aset")->name("maintenance-asset.")->group(function () {
        Route::get("/dashboard", [AssetMaintenanceHomeController::class, "index"])->name("home.index");
        // Alkes
        Route::prefix("alkes")->name("alkes.")->group(function () {
            Route::get("jadwal", [MaintenanceMedicalScheduleController::class, "index"])->name("schedule.index");
            Route::get("jadwal-list", [MaintenanceMedicalScheduleController::class, "list"])->name("schedule.list");
            Route::get("jadwal/tambah", [MaintenanceMedicalScheduleController::class, "create"])->name("schedule.create");
            Route::get("jadwal/detail/{maintenanceSchedule:id}", [MaintenanceMedicalScheduleController::class, "show"])->name("schedule.show");
            Route::post("jadwal/store", [MaintenanceMedicalScheduleController::class, "store"])->name("schedule.store");
            Route::delete("jadwal/{maintenanceSchedule:id}", [MaintenanceMedicalScheduleController::class, "destroy"])->name("schedule.destroy");

            Route::get("kegiatan", [MaintenanceMedicalActivityController::class, "index"])->name("activity.index");
            Route::get("kegiatan-list", [MaintenanceMedicalActivityController::class, "list"])->name("activity.list");
            Route::get("kegiatan/tambah", [MaintenanceMedicalActivityController::class, "create"])->name("activity.create");
            Route::get("kegiatan/detail/{maintenanceActivity:id}", [MaintenanceMedicalActivityController::class, "show"])->name("activity.show");
            Route::post("kegiatan/store", [MaintenanceMedicalActivityController::class, "store"])->name("activity.store");
            Route::delete("kegiatan/{maintenanceActivity:id}", [MaintenanceMedicalActivityController::class, "destroy"])->name("activity.destroy");
        });

        // Non Alkes
        Route::prefix("non-alkes")->name("non-alkes.")->group(function () {
            Route::get("jadwal", [MaintenanceNonMedicalScheduleController::class, "index"])->name("schedule.index");
            Route::get("jadwal-list", [MaintenanceNonMedicalScheduleController::class, "list"])->name("schedule.list");
            Route::get("jadwal/tambah", [MaintenanceNonMedicalScheduleController::class, "create"])->name("schedule.create");
            Route::get("jadwal/detail/{maintenanceSchedule:id}", [MaintenanceNonMedicalScheduleController::class, "show"])->name("schedule.show");
            Route::post("jadwal/store", [MaintenanceNonMedicalScheduleController::class, "store"])->name("schedule.store");
            Route::delete("jadwal/{maintenanceSchedule:id}", [MaintenanceNonMedicalScheduleController::class, "destroy"])->name("schedule.destroy");

            Route::get("kegiatan", [MaintenanceNonMedicalActivityController::class, "index"])->name("activity.index");
            Route::get("kegiatan-list", [MaintenanceNonMedicalActivityController::class, "list"])->name("activity.list");
            Route::get("kegiatan/tambah", [MaintenanceNonMedicalActivityController::class, "create"])->name("activity.create");
            Route::get("kegiatan/detail/{maintenanceActivity:id}", [MaintenanceNonMedicalActivityController::class, "show"])->name("activity.show");
            Route::post("kegiatan/store", [MaintenanceNonMedicalActivityController::class, "store"])->name("activity.store");
            Route::delete("kegiatan/{maintenanceActivity:id}", [MaintenanceNonMedicalActivityController::class, "destroy"])->name("activity.destroy");
        });

        // Incidental
        Route::prefix("isidental")->name("incidental.")->group(function () {
            Route::get("request", [MaintenanceIncidentalRequestController::class, "index"])->name("request.index");
            Route::get("request-list", [MaintenanceIncidentalRequestController::class, "list"])->name("request.list");
            Route::get("request/detail/{incidentalRequest:id}", [MaintenanceIncidentalRequestController::class, "show"])->name("request.show");
            Route::delete("request/{incidentalRequest:id}", [MaintenanceIncidentalRequestController::class, "destroy"])->name("request.destroy");
            Route::get("request/tambah", [MaintenanceIncidentalRequestController::class, "create"])->name("request.create");
            Route::post("request/store", [MaintenanceIncidentalRequestController::class, "store"])->name("request.store");

            Route::get("keputusan", [MaintenanceIncidentalDecisionController::class, "index"])->name("decision.index");
            Route::get("keputusan-list", [MaintenanceIncidentalDecisionController::class, "list"])->name("decision.list");
            Route::get("keputusan/data/{incidentalRequest:id}", [MaintenanceIncidentalDecisionController::class, "data"])->name("decision.data");
            Route::get("keputusan/detail/{incidentalRequest:id}", [MaintenanceIncidentalDecisionController::class, "show"])->name("decision.show");
            Route::post("keputusan/store", [MaintenanceIncidentalDecisionController::class, "store"])->name("decision.store");
            Route::put("keputusan/detail/{incidentalRequest:id}", [MaintenanceIncidentalDecisionController::class, "update"])->name("decision.update");

            Route::get("kegiatan", [MaintenanceIncidentalActivityController::class, "index"])->name("activity.index");
            Route::get("kegiatan-list", [MaintenanceIncidentalActivityController::class, "list"])->name("activity.list");
            Route::put("kegiatan/detail/{incidentalRequest:id}", [MaintenanceIncidentalActivityController::class, "update"])->name("activity.update");
        });

        // Report
        Route::prefix("laporan")->name("laporan.")->group(function () {
            Route::get("laporan-pemeliharaan", [MaintenanceReportController::class, "index"])->name("report.index");
            Route::get("laporan-jadwal", [MaintenanceReportController::class, "jadwal"])->name("report.jadwal");
            Route::get("laporan-detail", [MaintenanceReportController::class, "detail"])->name("report.detail");
            Route::get("laporan-insidental", [MaintenanceReportController::class, "log_incidental"])->name("report.log_incidental");
            Route::get("export-detail", [MaintenanceReportController::class, "export_detail"])->name("export.detail");
            Route::get("export-pemeliharaan", [MaintenanceReportController::class, "export_pemeliharaan"])->name("export.pemeliharaan");
            Route::get("export-incidental", [MaintenanceReportController::class, "export_incidental"])->name("report.export_incidental");
            Route::get("export-jadwal", [MaintenanceReportController::class, "export_schedule"])->name("report.export_schedule");
        });
    });

    // Logistic
    Route::prefix("logistik")->name("logistic.")->group(function () {
        Route::get("/dashboard", [LogisticHomeController::class, "index"])->name("home.index");
        // Asset Logistic
        Route::get("data-aset", [AssetLogisticController::class, "index"])->name("asset.index");
        Route::get("data-aset-list", [AssetLogisticController::class, "list"])->name("asset.list");
        Route::get("data-aset/detail/{assetEntry:id}", [AssetLogisticController::class, "show"])->name("asset.show");
        Route::get("data-aset/tambah", [AssetLogisticController::class, "create"])->name("asset.create");
        Route::get("dropdown/item", [AssetLogisticController::class, "dropdown_item"])->name("asset.dropdown.item");
        Route::get("data-aset/example-import", [AssetLogisticController::class, "example_import"])->name("asset.example.import");
        Route::get("data-aset/example-import-saldo", [AssetLogisticController::class, "example_import_incoming"])->name("asset.example.incoming");
        Route::post("data-aset/{assetEntry:id}/status", [AssetLogisticController::class, "change_status"])->name("asset.status");
        Route::post("data-aset/store", [AssetLogisticController::class, "store"])->name("asset.store");
        Route::post("data-aset/import", [AssetLogisticController::class, "import"])->name("asset.import");
        Route::post("data-aset/import-saldo-awal", [AssetLogisticController::class, "import_incoming"])->name("asset.import.incoming");
        Route::delete("data-aset/{assetEntry:id}", [AssetLogisticController::class, "destroy"])->name("asset.destroy");
        Route::get("data-aset/export-excel", [AssetLogisticController::class, "exportExcel"])->name("asset.export.excel");
        Route::get("data-aset/export-pdf", [AssetLogisticController::class, "exportPdf"])->name("asset.export.pdf");

        // Kartu Stock Export
        Route::get("kartu-stock/export", [LogisticReportController::class, "exportKartuStock"])->name("kartu-stock.export");

        // Incoming Asset
        Route::get("barang-masuk", [IncomingAssetController::class, "index"])->name("incoming-asset.index");
        Route::get("barang-masuk-list", [IncomingAssetController::class, "list"])->name("incoming-asset.list");
        Route::get("barang-masuk/tambah", [IncomingAssetController::class, "create"])->name("incoming-asset.create");
        Route::get("barang-masuk/detail/{logistic:id}", [IncomingAssetController::class, "show"])->name("incoming-asset.show");
        Route::post("barang-masuk/store", [IncomingAssetController::class, "store"])->name("incoming-asset.store");
        Route::delete("barang-masuk/{logistic:id}", [IncomingAssetController::class, "destroy"])->name("incoming-asset.destroy");


        // Adjustment Asset
        Route::get("penyesuaian-logistik", [AdjustmentAssetController::class, "index"])->name("stock-adjustment.index");
        Route::get("penyesuaian-logistik-list", [AdjustmentAssetController::class, "list"])->name("stock-adjustment.list");
        Route::get("penyesuaian-logistik/tambah", [AdjustmentAssetController::class, "create"])->name("stock-adjustment.create");
        Route::get("penyesuaian-logistik/detail/{logistic:id}", [AdjustmentAssetController::class, "show"])->name("stock-adjustment.show");
        Route::post("penyesuaian-logistik/store", [AdjustmentAssetController::class, "store"])->name("stock-adjustment.store");
        Route::delete("penyesuaian-logistik/{logistic:id}", [AdjustmentAssetController::class, "destroy"])->name("stock-adjustment.destroy");

        // Asset Request
        Route::get("permintaan-barang", [AssetRequestController::class, "index"])->name("asset-request.index");
        Route::get("permintaan-barang-list", [AssetRequestController::class, "list"])->name("asset-request.list");
        Route::get("permintaan-barang/tambah", [AssetRequestController::class, "create"])->name("asset-request.create");
        Route::get("permintaan-barang/detail/{requestLogistic:id}", [AssetRequestController::class, "show"])->name("asset-request.show");
        Route::post("permintaan-barang/store", [AssetRequestController::class, "store"])->name("asset-request.store");
        Route::delete("permintaan-barang/{requestLogistic:id}", [AssetRequestController::class, "destroy"])->name("asset-request.destroy");
        Route::get("permintaan-barang/print/{requestLogistic:id}", [AssetRequestController::class, "print"])->name("asset-request.print");

        // Asset Fulfillment
        Route::get("pemenuhan-barang", [AssetFulfillmentController::class, "index"])->name("asset-fulfillment.index");
        Route::get("pemenuhan-barang-list", [AssetFulfillmentController::class, "list"])->name("asset-fulfillment.list");
        Route::get("pemenuhan-barang/tambah", [AssetFulfillmentController::class, "create"])->name("asset-fulfillment.create");
        Route::get("pemenuhan-barang/detail/{requestLogistic:id}", [AssetFulfillmentController::class, "show"])->name("asset-fulfillment.show");
        Route::delete("pemenuhan-barang/{requestLogistic:id}", [AssetFulfillmentController::class, "destroy"])->name("asset-fulfillment.destroy");
        Route::post("pemenuhan-barang", [AssetFulfillmentController::class, "store"])->name("asset-fulfillment.store");
        Route::put("pemenuhan-barang/detail/{requestLogistic:id}", [AssetFulfillmentController::class, "update"])->name("asset-fulfillment.update");

        // Outgoing Item
        Route::get("barang-keluar", [OutgoingItemController::class, "index"])->name("outgoing-item.index");
        Route::get("barang-keluar-list", [OutgoingItemController::class, "list"])->name("outgoing-asset.list");
        Route::get("barang-keluar/detail/{logistic:id}", [OutgoingItemController::class, "show"])->name("outgoing-asset.show");
        Route::delete("barang-keluar/{logistic:id}", [OutgoingItemController::class, "destroy"])->name("outgoing-asset.destroy");
        Route::get('barang-keluar/print', [OutgoingItemController::class, 'print'])->name('logistic.outgoing-item.print');

        // Stock Recap
        Route::get("rekapitulasi-stock", [StockRecapController::class, "index"])->name("stock-recap.index");
        Route::get("rekapitulasi-stock-list", [StockRecapController::class, "list"])->name("stock-recap.list");
        Route::get("rekapitulasi-stock/tambah", [StockRecapController::class, "create"])->name("stock-recap.create");
        Route::get("rekapitulasi-stock/detail/{id}", [StockRecapController::class, "show"])->name("stock-recap.show");
        Route::post("rekapitulasi-stock/store", [StockRecapController::class, "store"])->name("stock-recap.store");
        Route::delete("rekapitulasi-stock/{id}", [StockRecapController::class, "destroy"])->name("stock-recap.destroy");

        Route::get("dropdown/config", [StockRecapController::class, "dropdown_config"])->name("stock.dropdown.config");
        Route::get("dropdown/config-recap", [StockRecapController::class, "dropdown_config_recap"])->name("stock-recap.dropdown.config");


        // Logistic Report
        Route::get("laporan-request-barang", [LogisticReportController::class, "request"])->name("report-logistic.request.index");
        Route::get("laporan-request-barang/export", [LogisticReportController::class, "requestExport"])->name("report-logistic.request.export");

        Route::get("laporan-barang-masuk", [LogisticReportController::class, "incoming"])->name("report-logistic.incoming.index");
        Route::get("laporan-barang-masuk/export", [LogisticReportController::class, "incomingExport"])->name("report-logistic.incoming.export");

        Route::get("laporan-barang-keluar", [LogisticReportController::class, "outgoing"])->name("report-logistic.outgoing.index");
        Route::get("laporan-barang-keluar/export", [LogisticReportController::class, "outgoingExport"])->name("report-logistic.outgoing.export");

        Route::get("laporan-penerimaan-pengeluaran-barang", [LogisticReportController::class, "inout"])->name("report-logistic.inout.index");
        Route::get("laporan-penerimaan-pengeluaran-barang/export", [LogisticReportController::class, "inoutExport"])->name("report-logistic.inout.export");

        Route::get("resume-barang-keluar", [LogisticReportController::class, "resumeoutgoing"])->name("report-logistic.resumeoutgoing.index");
        Route::get("resume-barang-keluar-data", [LogisticReportController::class, "resumeoutgoingData"])->name("report-logistic.resumeoutgoing.data");
        Route::get("resume-barang-keluar/{room:id}", [LogisticReportController::class, "resumeoutgoingShow"])->name("report-logistic.resumeoutgoing.show");
        Route::get("resume-barang-keluar/{room}/export", [LogisticReportController::class, "resumeoutgoingExport"])->name("report-logistic.resumeoutgoing.export-excel");

        Route::get("laporan-stock-opname", [LogisticReportController::class, "stockopname"])->name("report-logistic.stock-opname.index");
        Route::get("laporan-buku-besar", [LogisticReportController::class, "generalledger"])->name("report-logistic.general-ledger.index");

        Route::get("laporan-rekap", [LogisticReportController::class, "recap"])->name("report-logistic.recap.index");
        Route::get("laporan-rekap/export", [LogisticReportController::class, "recapExport"])->name("report-logistic.recap.export");

        Route::get('laporan/stock-opname/export-pdf', [LogisticReportController::class, 'exportStockOpnamePdf'])->name('logistic.report.stock-opname.export-pdf');
        Route::get('laporan/stock-opname/export-excel', [LogisticReportController::class, 'exportStockOpnameExcel'])->name('logistic.report.stock-opname.export-excel');
    });

    // Depreciation
    Route::prefix("penyusutan")->name("depreciation.")->group(function () {
        Route::get("/dashboard", [DepreciationHomeController::class, "index"])->name("home.index");
        // Report
        Route::get("laporan", [DepreciationReportController::class, "index"])->name("report.index");
        Route::get("report/data", [DepreciationReportController::class, "getData"])->name("report.data");
        Route::get("report/export", [DepreciationReportController::class, "export"])->name("report.export");

        // Setting
        Route::get("setting", [DepreciationSettingController::class, "index"])->name("setting.index");
    });

    // Planning
    Route::prefix("perencanaan")->name("planning.")->group(function () {
        Route::get("/dashboard", [PlanningHomeController::class, "index"])->name("home.index");
        // Data Asset
        Route::get("data-aset", [AssetInfrastructureController::class, "index"])->name("asset.index");

        // Consumable Procurement
        Route::get("perencanaan-pengadaan", [ConsumableProcurementController::class, "index"])->name("consumable-procurement.index");
        Route::get("perencanaan-pengadaan-list", [ConsumableProcurementController::class, "list"])->name("consumable-procurement.list");
        Route::get("perencanaan-pengadaan/tambah", [ConsumableProcurementController::class, "create"])->name("consumable-procurement.create");
        Route::get("/perencanaan-pengadaan/{plan:id}/show", [ConsumableProcurementController::class, "show"])->name("consumable-procurement.show");
        Route::get("perencanaan-pengadaan/dropdown/program", [ConsumableProcurementController::class, "_dropdownProgram"])->name("consumable-procurement.dropdown.program");
        Route::get("perencanaan-pengadaan/dropdown/kegiatan", [ConsumableProcurementController::class, "_dropdownKegiatan"])->name("consumable-procurement.dropdown.kegiatan");
        Route::get("perencanaan-pengadaan/dropdown/program-output", [ConsumableProcurementController::class, "_dropdownProgramOutput"])->name("consumable-procurement.dropdown.program-output");
        Route::post("perencanaan-pengadaan/store", [ConsumableProcurementController::class, "store"])->name("consumable-procurement.store");
        Route::get("perencanaan-pengadaan/{plan}/export", [ConsumableProcurementController::class, "exportExcel"])->name("consumable-procurement.export");
        Route::delete("perencanaan-pengadaan/{plan:id}", [ConsumableProcurementController::class, "destroy"])->name("consumable-procurement.destroy");

        // Approval Consumable Procurement
        Route::get("/pengajuan-perencanaan-pengadaan", [ApprovalConsumableProcurementController::class, "index"])->name("approval-consumable-procurement.index");
        Route::get("/pengajuan-perencanaan-pengadaan-list", [ApprovalConsumableProcurementController::class, "list"])->name("approval-consumable-procurement.list");
        Route::post("/pengajuan-perencanaan-pengadaan/{plan:id}/approve", [ApprovalConsumableProcurementController::class, "approve"])->name("approval-consumable-procurement.approve");

        // Asset Maintenance
        Route::get("/pemeliharaan-barang", [AssetMaintenanceController::class, "index"])->name("asset-maintenance.index");
        Route::get("/pemeliharaan-barang-list", [AssetMaintenanceController::class, "list"])->name("asset-maintenance.list");
        Route::get("/pemeliharaan-barang/tambah", [AssetMaintenanceController::class, "create"])->name("asset-maintenance.create");
        Route::get("/pemeliharaan-barang/{maintenancePlan:id}/show", [AssetMaintenanceController::class, "show"])->name("asset-maintenance.show");
        Route::post("/pemeliharaan-barang/store", [AssetMaintenanceController::class, "store"])->name("asset-maintenance.store");
        Route::delete("/pemeliharaan-barang/{id}", [AssetMaintenanceController::class, "destroy"])->name("asset-maintenance.destroy");
        Route::get("/program-output-maintenance", [AssetMaintenanceController::class, "getProgramOutput"])->name("program-output-maintenance");

        // Approval Asset Maintenance
        Route::get("/pengajuan-pemeliharaan-barang", [ApprovalAssetMaintenanceController::class, "index"])->name("approval-asset-maintenance.index");
        Route::get("/pengajuan-pemeliharaan-barang-list", [ApprovalAssetMaintenanceController::class, "list"])->name("approval-asset-maintenance.list");
        Route::post("/pengajuan-pemeliharaan-barang/{maintenancePlan:id}/approve", [ApprovalAssetMaintenanceController::class, "approve"])->name("approval-asset-maintenance.approve");

        // Report
        Route::get("laporan/perencanaan", [PlanningReportController::class, "planning"])->name("report.planning");
        Route::get("laporan/pemeliharaan", [PlanningReportController::class, "maintenance"])->name("report.maintenance");
        Route::get("laporan-perencanaan/export", [PlanningReportController::class, "export"])->name("planning-report.export");
        Route::get("laporan-pemeliharaan/export", [PlanningReportController::class, "exportMaintenance"])->name("maintenance-report.export");
    });

    // Dropdown
    Route::prefix("dropdown")->name("dropdown.")->group(function () {
        Route::get("role", [RoleController::class, "dropdown"])->name("role");
        Route::get("permission", [RoleController::class, "dropdown_permission"])->name("permission");
        Route::get("category", [CategoryController::class, "dropdown"])->name("category");

        Route::get("distributor", [DistributorController::class, "dropdown"])->name("distributor");
        Route::get("item", [ItemController::class, "dropdown"])->name("item");
        Route::get("uom", [UomController::class, "dropdown"])->name("uom");
        Route::get("room", [RoomController::class, "dropdown"])->name("room");
        Route::get("room-with-access", [RoomController::class, "dropdown_with_access"])->name("room-with-access");
        Route::get("room-with-access-with-trigger", [RoomController::class, "dropdown_with_access_with_trigger"])->name("room-with-access-with-trigger");
        Route::get("room-with-access-with-plan-approve", [RoomController::class, "dropdown_with_access_planapprove"])->name("room-with-access-with-plan-approve");
        Route::get("room-category", [RoomCategoryController::class, "dropdown"])->name("room-category");
        Route::get("asset", [AssetController::class, "dropdown"])->name("asset");
        Route::get("logistic", [AssetLogisticController::class, "dropdown"])->name("logistic");
        Route::get("employee", [EmployeeController::class, "dropdown"])->name("employee");
        Route::get("employee-room", [EmployeeController::class, "_dropdown"])->name("employee.room");
        Route::get("employee-party-two", [EmployeeController::class, "dropdownPartyTwo"])->name("employee.partyTwo");
        Route::get("aset-rusak", [DamagedAssetController::class, "dropdown"])->name("damaged-asset");
        Route::get("officer", [OfficerController::class, "dropdown"])->name("officer");
        Route::get("program", [AssetMaintenanceController::class, "_dropdownProgram"])->name("program");
        Route::get("program-output", [AssetMaintenanceController::class, "_dropdownProgramOutput"])->name("program.output");
        Route::get("room-program-output", [ProgramController::class, "dropdown_output"])->name("room.program.output");
        Route::get("maintenance-category", [AssetMaintenanceController::class, "_dropdownPemeliharaan"])->name("maintenance-category");
        Route::get("program-top-parent", [ProgramController::class, "dropdown_top_parent"])->name("program-top-parent");
        Route::get("program-parent-id", [ProgramController::class, "dropdown_parent_id"])->name("program-parent-id");
    });

    // Detail Asset
    Route::get("asset/{asset:id}", [AssetController::class, "show"])->name("asset.show");
    Route::get("asset-entry/{assetEntry:id}", [AssetController::class, "showEntry"])->name("asset.show-entry");

    // Export Maintenance Plan
    Route::get('/perencanaan/pemeliharaan-barang/{id}/export', [AssetMaintenanceController::class, 'export'])
        ->name('planning.asset-maintenance.export');
});
