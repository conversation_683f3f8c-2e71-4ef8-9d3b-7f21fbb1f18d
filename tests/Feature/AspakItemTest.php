<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\AspakItem;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class AspakItemTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a user for authentication
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password')
        ]);
    }

    public function test_aspak_item_index_page_loads()
    {
        $response = $this->actingAs($this->user)
                         ->get('/master-data/aspak/item');

        $response->assertStatus(200);
        $response->assertViewIs('master-data.aspak.item.index');
    }

    public function test_aspak_item_list_api_returns_json()
    {
        // Create some test data
        AspakItem::factory()->branch()->create([
            'item_name' => 'Test Item 1',
            'item_code' => 'TEST001'
        ]);

        $response = $this->actingAs($this->user)
                         ->getJson('/master-data/aspak/item/list');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'draw',
            'recordsTotal',
            'recordsFiltered',
            'data' => [
                '*' => [
                    'id',
                    'item_name',
                    'item_code',
                    'tree',
                    'action'
                ]
            ]
        ]);
    }

    public function test_aspak_item_parent_groups_api_works()
    {
        // Create a BRANCH item
        AspakItem::factory()->branch()->create([
            'item_name' => 'Parent Branch',
            'item_code' => 'PARENT001'
        ]);

        $response = $this->actingAs($this->user)
                         ->getJson('/master-data/aspak/item/parent-groups');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            '*' => [
                'id',
                'text'
            ]
        ]);
    }

    public function test_aspak_item_store_validation_works()
    {
        $response = $this->actingAs($this->user)
                         ->postJson('/master-data/aspak/item', []);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['item_name', 'tree']);
    }

    public function test_aspak_item_can_be_created()
    {
        $data = [
            'item_name' => 'New Test Item',
            'item_code' => 'NEW001',
            'item_synonym' => 'Test Synonym',
            'tree' => 'BRANCH'
        ];

        $response = $this->actingAs($this->user)
                         ->postJson('/master-data/aspak/item', $data);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);
        
        $this->assertDatabaseHas('aspak_items', [
            'item_name' => 'New Test Item',
            'item_code' => 'NEW001'
        ]);
    }
}
